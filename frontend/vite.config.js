import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import path from 'path'

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())

  // Use VITE_API_BASE_URL from environment, fallback to localhost for local development
  const proxyTarget = env.VITE_API_BASE_URL || 'http://localhost:8000'

  return {
    plugins: [vue()],
    server: {
      port: 5173,
      host: true,
      allowedHosts: ['herbit-dev.pride.improwised.dev'],
      proxy: {
        '/api': {
          target: proxyTarget,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path
        },
        '/auth': {
          target: proxyTarget,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => path
        }
      }
    },
    resolve: {
      alias: {
        '@': path.resolve(__dirname, './src')
      }
    },
    assetsInclude: ['**/*.svg'],
    build: {
      rollupOptions: {
        output: {
          manualChunks: {
            'svg-icons': []
          }
        }
      }
    }
  }
})
