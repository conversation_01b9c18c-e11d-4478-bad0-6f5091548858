/** @type {import('tailwindcss').Config} */
export default {
    darkMode: ["class"],
    content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
  	extend: {
      typography: {
        DEFAULT: {
          css: {
            maxWidth: '100%',
            color: 'inherit',
          },
        },
        invert: {
          css: {
            '--tw-prose-body': 'var(--tw-prose-invert-body)',
            '--tw-prose-headings': 'var(--tw-prose-invert-headings)',
            '--tw-prose-links': 'var(--tw-prose-invert-links)',
            '--tw-prose-links-hover': 'var(--tw-prose-invert-links-hover)',
            '--tw-prose-underline': 'var(--tw-prose-invert-underline)',
            '--tw-prose-underline-hover': 'var(--tw-prose-invert-underline-hover)',
            '--tw-prose-bold': 'var(--tw-prose-invert-bold)',
            '--tw-prose-counters': 'var(--tw-prose-invert-counters)',
            '--tw-prose-bullets': 'var(--tw-prose-invert-bullets)',
            '--tw-prose-hr': 'var(--tw-prose-invert-hr)',
            '--tw-prose-quote-borders': 'var(--tw-prose-invert-quote-borders)',
            '--tw-prose-captions': 'var(--tw-prose-invert-captions)',
            '--tw-prose-code': 'var(--tw-prose-invert-code)',
            '--tw-prose-code-bg': 'var(--tw-prose-invert-code-bg)',
            '--tw-prose-pre-code': 'var(--tw-prose-invert-pre-code)',
            '--tw-prose-pre-bg': 'var(--tw-prose-invert-pre-bg)',
            '--tw-prose-pre-border': 'var(--tw-prose-invert-pre-border)',
            '--tw-prose-th-borders': 'var(--tw-prose-invert-th-borders)',
            '--tw-prose-td-borders': 'var(--tw-prose-invert-td-borders)',
          },
        },
      },
  		fontFamily: {
  			sans: [
  				'Inter',
  				'SF Pro Display',
  				'-apple-system',
  				'BlinkMacSystemFont',
  				'Segoe UI',
  				'Roboto',
  				'Oxygen',
  				'Ubuntu',
  				'Cantarell',
  				'Open Sans',
  				'Helvetica Neue',
  				'sans-serif'
  			],
  			mono: [
  				'Fira Code',
  				'Source Code Pro',
  				'ui-monospace',
  				'SFMono-Regular',
  				'Menlo',
  				'Monaco',
  				'Consolas',
  				'Liberation Mono',
  				'Courier New',
  				'monospace'
  			]
  		},
  		animation: {
  			'fade-in': 'fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards',
  			'fade-in-delay': 'fadeIn 1.5s cubic-bezier(0.4, 0, 0.2, 1) forwards',
  			'fade-in-up': 'fadeInUp 0.6s cubic-bezier(0.22, 0.61, 0.36, 1) forwards',
  			'bounce-slow': 'bounceSlow 3s infinite',
  			'float': 'float 6s ease-in-out infinite',
  			'float-particle': 'floatParticle 20s linear infinite',
  			'pulse-glow': 'pulseGlow 4s ease-in-out infinite',
  			'bg-shift': 'bgShift 15s ease infinite',
  			pulse: 'pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite',
  			'fadeIn': 'fadeIn 0.3s ease-out forwards',
  			'scaleIn': 'scaleIn 0.3s ease-out forwards'
  		},
  		keyframes: {
  			fadeIn: {
  				'0%': {
  					opacity: '0',
  					transform: 'translateY(-15px) scale(0.98)'
  				},
  				'100%': {
  					opacity: '1',
  					transform: 'translateY(0) scale(1)'
  				}
  			},
  			scaleIn: {
  				'0%': {
  					opacity: '0',
  					transform: 'scale(0.95)'
  				},
  				'100%': {
  					opacity: '1',
  					transform: 'scale(1)'
  				}
  			},
  			fadeInUp: {
  				from: {
  					opacity: '0',
  					transform: 'translateY(20px)'
  				},
  				to: {
  					opacity: '1',
  					transform: 'translateY(0)'
  				}
  			},
  			bounceSlow: {
  				'0%, 100%': {
  					transform: 'translateY(0)'
  				},
  				'50%': {
  					transform: 'translateY(-8px)'
  				}
  			},
  			float: {
  				'0%, 100%': {
  					transform: 'translateY(0)'
  				},
  				'50%': {
  					transform: 'translateY(-15px)'
  				}
  			},
  			floatParticle: {
  				'0%': {
  					transform: 'translateY(0) translateX(0)'
  				},
  				'25%': {
  					transform: 'translateY(-30px) translateX(30px)'
  				},
  				'50%': {
  					transform: 'translateY(0) translateX(60px)'
  				},
  				'75%': {
  					transform: 'translateY(30px) translateX(30px)'
  				},
  				'100%': {
  					transform: 'translateY(0) translateX(0)'
  				}
  			},
  			pulseGlow: {
  				'0%, 100%': {
  					opacity: '0.7',
  					filter: 'blur(20px)'
  				},
  				'50%': {
  					opacity: '0.3',
  					filter: 'blur(15px)'
  				}
  			},
  			bgShift: {
  				'0%': {
  					backgroundPosition: '0% 50%'
  				},
  				'50%': {
  					backgroundPosition: '100% 50%'
  				},
  				'100%': {
  					backgroundPosition: '0% 50%'
  				}
  			}
  		},
  		fontSize: {
  			xxs: [
  				'0.65rem',
  				'0.9'
  			],
  			'text-xxs': [
  				'0.55rem',
  				'0.85'
  			],
  			ascii: [
  				'1rem',
  				'1'
  			],
  			'ascii-sm': [
  				'0.875rem',
  				'0.9'
  			],
  			'ascii-xs': [
  				'0.75rem',
  				'0.85'
  			]
  		},
  		scale: {
  			'15': '0.15',
  			'20': '0.20',
  			'25': '0.25',
  			'35': '0.35',
  			'40': '0.40',
  			'45': '0.45'
  		},
  		backgroundImage: {
  			'gradient-underline': 'linear-gradient(to right, #3898FF, #7B5FFD)',
  			'gradient-phantom': 'linear-gradient(to right, #3898FF, #7B5FFD)',
  			'gradient-phantom-dark': 'linear-gradient(to right, #0A0E17, #131B2E)'
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)',
  			'2xl': '1rem',
  			'3xl': '1.5rem',
  			'4xl': '2rem'
  		},
  		boxShadow: {
  			'glow-sm': '0 0 15px rgba(56, 152, 255, 0.3)',
  			'glow-md': '0 0 25px rgba(56, 152, 255, 0.4)',
  			'glow-lg': '0 0 35px rgba(56, 152, 255, 0.5)',
  			'glow-xl': '0 0 50px rgba(56, 152, 255, 0.6)'
  		},
  		colors: {
  			// Phantom-inspired colors
  			phantom: {
  				blue: '#3898FF',
  				indigo: '#7B5FFD',
  				purple: '#9D5FFF',
  				dark: '#0A0E17',
  				'dark-blue': '#131B2E',
  				gray: '#8A919E',
  				'light-gray': '#C5CAD3'
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		backdropBlur: {
  			xs: '2px',
  			sm: '4px',
  			DEFAULT: '8px',
  			md: '12px',
  			lg: '16px',
  			xl: '24px',
  			'2xl': '40px',
  			'3xl': '64px',
  		}
  	}
  },
  plugins: [
    require("tailwindcss-animate"),
    require("@tailwindcss/typography")
],
}
