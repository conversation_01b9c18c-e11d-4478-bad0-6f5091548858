# Quiz Management UI

This project is an Vue.js application designed for managing quizzes. It includes features such as user role-based access, adding skills, quiz generation, listing generated quizzes, selecting specific questions, finalizing questions, and generating the final assessments.

## Features

- **User Role-Based Access**: Different functionalities are available based on user roles (admin or regular user).
- **Add Skills**: Users can add specific skills required for quizzes.
- **Quiz Generation**: Users can create new quizzes by providing necessary details.
- **Quiz Listing**: View and manage a list of generated quizzes.
- **Question Selection**: Select specific questions to include in quizzes.
- **Finalize Quiz**: Review and confirm selected questions before generating the final quiz.

## Setup Instructions

1. Clone the repository:

   ```
   git clone https://git.pride.improwised.dev/Improwised/herbit.git
   cd frontend
   ```

2. Install dependencies:

   ```
   npm install
   ```

   **Make sure to setup frontend/.env file before running the application**

3. Run the application:

   ```
   npm run dev
   ```

4. Open your browser and navigate to `http://localhost:5173` to view the application.

## Usage Guidelines

- Ensure you have Node.js and npm installed on your machine.
- Follow the instructions to set up the project and run it locally.
- Explore the different features available in the application based on your user role.

## Contributing

Contributions are welcome! Please feel free to submit a pull request or open an issue for any enhancements or bug fixes.
