# Herbit GUI Implementation

This document outlines the implementation of the GUI version of the Herbit CLI application.

## Overview

We've implemented a GUI version of the Herbit CLI application, which provides a user-friendly interface for managing assessments, skills, sessions, and reports. The GUI uses the same API endpoints as the CLI application, ensuring consistency in functionality.

## Components Implemented

1. **API Service**

   - Created a comprehensive API service (`src/services/api.js`) that handles all API calls to the backend
   - Implemented error handling and proper response parsing

2. **Admin Components**

   - **CreateAssessment.vue**: Create mock and final assessments based on skills
   - **CreateSkill.vue**: Create skills with descriptions for question generation
   - **ListSkills.vue**: View all available skills
   - **GenerateSessions.vue**: Generate session codes for assessments
   - **AddFinalQuestions.vue**: Add final questions to assessments
   - **DateWiseReport.vue**: Generate and download date-wise reports

3. **Navigation**
   - **AdminHome.vue**: Central dashboard for accessing all admin features

## API Endpoints Used

The following API endpoints are used in the GUI implementation:

1. **Skills Management**

   - `/get_skills`: Fetch all skills
   - `/create_skill`: Create a new skill

2. **Assessments Management**

   - `/get_assessments`: Fetch all assessments
   - `/create_quiz`: Create new assessments (mock and final)
   - `/insert_final_questions`: Add final questions to an assessment

3. **Sessions Management**

   - `/generate_sessions`: Generate session codes for an assessment

4. **Reports Generation**

   - `/generate_report`: Generate various types of reports (date-wise, user-wise, assessment-wise)

5. **Question Management**
   - `/get_question_counts`: Get question counts for different difficulty levels

## Database Operations

The GUI implementation interacts with the same database tables as the CLI application:

1. **Skills Table**: Stores skill information
2. **Assessments Table**: Stores assessment information
3. **Assessment_Skills Table**: Maps skills to assessments
4. **Sessions Table**: Stores session information
5. **Questions Table**: Stores questions
6. **Final_Questions Table**: Stores final questions
7. **User_Answers Table**: Stores user answers
8. **User_Assessment Table**: Legacy table for backward compatibility

## Future Enhancements

1. **User Authentication**: Implement proper user authentication and authorization
2. **Real-time Updates**: Add WebSocket support for real-time updates
3. **Bulk Operations**: Add support for bulk operations (e.g., bulk upload of questions)
4. **Advanced Filtering**: Add advanced filtering options for reports and listings
5. **Data Visualization**: Add charts and graphs for better data visualization

## Notes

- The GUI implementation maintains feature parity with the CLI application
- All database operations are performed through the API endpoints
- The UI is designed to be responsive and user-friendly
