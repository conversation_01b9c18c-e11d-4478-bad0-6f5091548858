<script setup>
import { cn } from '@/lib/utils';
import { Check } from 'lucide-vue-next';
import {
  SelectItem,
  SelectItemIndicator,
  SelectItemText,
  useForwardProps,
} from 'reka-ui';
import { computed, toRefs } from 'vue';

const props = defineProps({
  value: { type: null, required: true },
  disabled: { type: Boolean, required: false },
  textValue: { type: String, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
  id: { type: String, required: false },
});

// Use toRefs to make props reactive before passing to useForwardProps

// Extract class separately and pass the rest directly to useForwardProps
const { class: className, ...restProps } = toRefs(props);
const forwardedProps = useForwardProps(restProps);
</script>

<template>
  <SelectItem
    v-bind="forwardedProps"
    :class="
      cn(
        'relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm text-white outline-none focus:bg-cyan-700 hover:bg-cyan-700 focus:text-white hover:text-white data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
        className?.value,
      )
    "
  >
    <span class="absolute right-2 flex h-3.5 w-3.5 items-center justify-center">
      <SelectItemIndicator>
        <Check class="h-4 w-4" />
      </SelectItemIndicator>
    </span>

    <SelectItemText>
      <slot />
    </SelectItemText>
  </SelectItem>
</template>
