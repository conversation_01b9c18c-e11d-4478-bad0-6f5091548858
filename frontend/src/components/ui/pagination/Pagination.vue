<template>
  <div class="pagination-container">
    <div class="flex items-center justify-center space-x-1 py-4">
      <!-- First Page Button -->
      <Transition name="slide-fade" appear>
        <button
          v-if="showFirstLast && currentPage > 3"
          @click="goToPage(1)"
          class="pagination-btn pagination-btn-nav group"
          :disabled="currentPage === 1"
          title="First page"
        >
          <svg class="w-4 h-4 transition-transform duration-300 group-hover:-translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
          </svg>
        </button>
      </Transition>

      <!-- Previous Button -->
      <Transition name="slide-fade" appear>
        <button
          @click="goToPage(currentPage - 1)"
          :disabled="currentPage === 1"
          class="pagination-btn pagination-btn-nav group"
          title="Previous page"
        >
          <svg class="w-4 h-4 transition-transform duration-300 group-hover:-translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
        </button>
      </Transition>

      <!-- Page Numbers -->
      <div class="flex items-center space-x-1">
        <!-- Visible page range only (max 3 pages) -->
        <TransitionGroup name="page-list" tag="div" class="flex items-center space-x-1">
          <button
            v-for="page in visiblePages"
            :key="page"
            @click="goToPage(page)"
            class="pagination-btn pagination-btn-number"
            :class="{ 'pagination-btn-active': currentPage === page }"
            :title="`Go to page ${page}`"
          >
            <span class="pagination-number">{{ page }}</span>
          </button>
        </TransitionGroup>
      </div>

      <!-- Next Button -->
      <Transition name="slide-fade" appear>
        <button
          @click="goToPage(currentPage + 1)"
          :disabled="currentPage === totalPages"
          class="pagination-btn pagination-btn-nav group"
          title="Next page"
        >
          <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
        </button>
      </Transition>

      <!-- Last Page Button -->
      <Transition name="slide-fade" appear>
        <button
          v-if="showFirstLast && currentPage < totalPages - 2"
          @click="goToPage(totalPages)"
          class="pagination-btn pagination-btn-nav group"
          :disabled="currentPage === totalPages"
          title="Last page"
        >
          <svg class="w-4 h-4 transition-transform duration-300 group-hover:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 5l7 7-7 7M5 5l7 7-7 7" />
          </svg>
        </button>
      </Transition>
    </div>


  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  currentPage: {
    type: Number,
    required: true,
    default: 1
  },
  totalPages: {
    type: Number,
    required: true,
    default: 1
  },
  totalItems: {
    type: Number,
    default: 0
  },
  itemsPerPage: {
    type: Number,
    default: 10
  },
  maxVisiblePages: {
    type: Number,
    default: 3
  },
  showFirstLast: {
    type: Boolean,
    default: true
  },
  showInfo: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['page-change'])

// Calculate visible page range
const startPage = computed(() => {
  const half = Math.floor(props.maxVisiblePages / 2)
  let start = props.currentPage - half

  if (start < 1) start = 1
  if (start + props.maxVisiblePages - 1 > props.totalPages) {
    start = Math.max(1, props.totalPages - props.maxVisiblePages + 1)
  }

  return start
})

const endPage = computed(() => {
  return Math.min(startPage.value + props.maxVisiblePages - 1, props.totalPages)
})

const visiblePages = computed(() => {
  const pages = []
  for (let i = startPage.value; i <= endPage.value; i++) {
    pages.push(i)
  }
  return pages
})

// Calculate item range for info display
const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

const goToPage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}
</script>

<style scoped>
.pagination-btn {
  @apply relative flex items-center justify-center min-w-[40px] h-10 rounded-lg font-medium text-sm
         transition-all duration-300 ease-out transform-gpu;
  transition-property: transform, background-color, border-color, box-shadow, color;
}

.pagination-btn:hover:not(:disabled) {
  @apply scale-105;
  transform: scale(1.05) translateY(-1px);
}

.pagination-btn:active:not(:disabled) {
  @apply scale-95;
  transform: scale(0.95) translateY(0px);
}

.pagination-btn:disabled {
  @apply opacity-40 cursor-not-allowed;
}

.pagination-btn-nav {
  @apply text-white/70 hover:text-white px-3;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-btn-nav:hover:not(:disabled) {
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
}

.pagination-btn-number {
  @apply text-white/80 hover:text-white relative overflow-hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-btn-number:hover:not(.pagination-btn-active) {
  text-shadow: 0 0 12px rgba(56, 152, 255, 0.8);
}

.pagination-btn-active {
  @apply text-phantom-blue;
  text-shadow: 0 0 15px rgba(56, 152, 255, 0.8), 0 0 30px rgba(56, 152, 255, 0.4);
  animation: activeGlow 2s ease-in-out infinite alternate;
}

@keyframes activeGlow {
  0% {
    text-shadow: 0 0 15px rgba(56, 152, 255, 0.8), 0 0 30px rgba(56, 152, 255, 0.4);
  }
  100% {
    text-shadow: 0 0 20px rgba(56, 152, 255, 1), 0 0 40px rgba(56, 152, 255, 0.6), 0 0 60px rgba(56, 152, 255, 0.3);
  }
}

.pagination-btn-active::before {
  content: '';
  @apply absolute inset-0 rounded-lg;
  background: linear-gradient(45deg, transparent, rgba(56, 152, 255, 0.1), transparent);
  animation: shimmer 2s ease-in-out infinite;
}

@keyframes shimmer {
  0% {
    opacity: 0.5;
    transform: translateX(-100%);
  }
  50% {
    opacity: 1;
    transform: translateX(0%);
  }
  100% {
    opacity: 0.5;
    transform: translateX(100%);
  }
}

.pagination-number {
  @apply relative z-10 transition-all duration-300;
}

.pagination-btn-number:hover .pagination-number {
  transform: scale(1.1) rotate(2deg);
}

.pagination-btn-active .pagination-number {
  animation: bounce 1s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

.pagination-ellipsis {
  @apply text-white/50 px-2 text-sm font-medium;
  animation: pulse 2s ease-in-out infinite;
}

/* Enhanced sweep animation */
.pagination-btn-number::after {
  content: '';
  @apply absolute inset-0 rounded-lg;
  background: linear-gradient(45deg, transparent, rgba(56, 152, 255, 0.3), transparent);
  transform: translateX(-100%) skewX(-15deg);
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.pagination-btn-number:hover::after {
  transform: translateX(100%) skewX(-15deg);
}

/* Ripple effect on click */
.pagination-btn-number {
  @apply relative overflow-hidden;
}

.pagination-btn-number:active::before {
  content: '';
  @apply absolute inset-0 bg-white/20 rounded-full;
  animation: ripple 0.6s ease-out;
}

@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}

/* Page transition animations */
.pagination-btn-number {
  position: relative;
}

.pagination-btn-number:not(.pagination-btn-active):hover {
  animation: wiggle 0.5s ease-in-out;
}

@keyframes wiggle {
  0%, 100% { transform: rotate(0deg); }
  25% { transform: rotate(1deg); }
  75% { transform: rotate(-1deg); }
}

/* Loading state animation - removed spinning circle */
.pagination-btn-active .pagination-number {
  position: relative;
}

/* Floating animation for navigation buttons */
.pagination-btn-nav:hover {
  animation: float 2s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) scale(1.05); }
  50% { transform: translateY(-2px) scale(1.05); }
}

/* Stagger animation for page numbers */
.pagination-btn-number:nth-child(1) { animation-delay: 0.1s; }
.pagination-btn-number:nth-child(2) { animation-delay: 0.2s; }
.pagination-btn-number:nth-child(3) { animation-delay: 0.3s; }
.pagination-btn-number:nth-child(4) { animation-delay: 0.4s; }
.pagination-btn-number:nth-child(5) { animation-delay: 0.5s; }

/* Mobile responsive adjustments */
@media (max-width: 640px) {
  .pagination-btn {
    @apply min-w-[36px] h-9 text-xs;
  }

  .pagination-btn-nav {
    @apply px-2;
  }

  .pagination-btn:hover:not(:disabled) {
    transform: scale(1.03) translateY(-1px);
  }
}

/* Dark mode enhancements */
@media (prefers-color-scheme: dark) {
  .pagination-btn-number:hover:not(.pagination-btn-active) {
    text-shadow: 0 0 15px rgba(56, 152, 255, 0.9);
  }
}

/* Focus states for accessibility */
.pagination-btn:focus-visible {
  outline: none;
  text-shadow: 0 0 0 2px rgba(56, 152, 255, 0.8);
}

/* Smooth entrance animation */
.pagination-btn {
  animation: fadeInUp 0.5s ease-out backwards;
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Vue Transition Styles */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-fade-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}

.slide-fade-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.page-fade-enter-active,
.page-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-fade-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(-10px);
}

.page-fade-leave-to {
  opacity: 0;
  transform: scale(0.8) translateY(10px);
}

.page-list-enter-active,
.page-list-leave-active {
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.page-list-enter-from {
  opacity: 0;
  transform: scale(0.5) rotate(180deg);
}

.page-list-leave-to {
  opacity: 0;
  transform: scale(0.5) rotate(-180deg);
}

.page-list-move {
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ellipsis-fade-enter-active,
.ellipsis-fade-leave-active {
  transition: all 0.3s ease-in-out;
}

.ellipsis-fade-enter-from,
.ellipsis-fade-leave-to {
  opacity: 0;
  transform: scale(0.5);
}



/* Enhanced ellipsis animation */
.ellipsis-dot {
  display: inline-block;
  animation: ellipsisPulse 1.5s ease-in-out infinite;
}

.ellipsis-dot:nth-child(1) { animation-delay: 0s; }
.ellipsis-dot:nth-child(2) { animation-delay: 0.2s; }
.ellipsis-dot:nth-child(3) { animation-delay: 0.4s; }

@keyframes ellipsisPulse {
  0%, 60%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  30% {
    opacity: 1;
    transform: scale(1.2);
  }
}



/* Container enhancements */
.pagination-container {
  position: relative;
}

.pagination-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100%;
  height: 1px;
  background: linear-gradient(90deg, transparent, rgba(56, 152, 255, 0.3), transparent);
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% {
    opacity: 0.3;
    width: 50%;
  }
  50% {
    opacity: 0.8;
    width: 100%;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .pagination-btn,
  .slide-fade-enter-active,
  .slide-fade-leave-active,
  .page-fade-enter-active,
  .page-fade-leave-active,
  .page-list-enter-active,
  .page-list-leave-active,
  .ellipsis-fade-enter-active,
  .ellipsis-fade-leave-active {
    transition: none !important;
    animation: none !important;
  }
}
</style>
