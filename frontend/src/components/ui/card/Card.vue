<script setup>
import { cn } from '@/lib/utils';
import { computed } from 'vue';

const props = defineProps({
  class: { type: null, required: false },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'form', 'table', 'download', 'glass'].includes(value)
  },
  color: {
    type: String,
    default: 'gray',
    validator: (value) => ['gray', 'cyan', 'blue', 'purple', 'teal', 'indigo'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'sm', 'lg', 'xl'].includes(value)
  },
  hover: {
    type: Boolean,
    default: false
  }
});

const cardClasses = computed(() => {
  // Removed rounded-xl, border, and shadow from baseClasses
  const baseClasses = 'relative transition-all duration-300 z-10';

  const variantClasses = {
    default: 'text-card-foreground', // Removed bg-card
    form: '', // Removed bg-gray-900 shadow-xl
    table: '', // Removed bg-gray-900 shadow-xl
    download: '', // Removed bg-gray-800
    glass: 'backdrop-blur-sm' // Removed bg-gray-900/80 and shadow-xl
  };

  const colorClasses = {
    gray: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-gray-600/50
    },
    cyan: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-cyan-500/50
    },
    blue: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-blue-500/50
    },
    purple: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-purple-500/50
    },
    teal: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-teal-500/50
    },
    indigo: {
      border: '', // Removed border-gray-800
      hover: props.hover ? '' : '' // Removed hover:border-indigo-500/50
    }
  };

  const sizeClasses = {
    default: 'p-6',
    sm: 'p-3',
    lg: 'p-8',
    xl: 'p-10'
  };

  return cn(
    baseClasses,
    variantClasses[props.variant],
    colorClasses[props.color].border,
    colorClasses[props.color].hover,
    sizeClasses[props.size],
    props.class
  );
});

const glowClasses = computed(() => {
  // Removed glow effect completely
  return '';
});

const shouldShowGlow = computed(() => {
  // Always return false to never show glow
  return false;
});
</script>

<template>
  <!-- For variants that need glow effect -->
  <div v-if="shouldShowGlow" class="relative group">
    <!-- Glow effect behind the card -->
    <div :class="glowClasses"></div>
    <!-- Main card -->
    <div :class="cardClasses">
      <slot />
    </div>
  </div>

  <!-- For variants that don't need glow effect -->
  <div v-else :class="cardClasses">
    <slot />
  </div>
</template>
