<template>
  <div class="w-full h-full">
    <apexchart
      type="heatmap"
      height="100%"
      :options="chartOptions"
      :series="series"
    ></apexchart>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  users: {
    type: Array,
    required: true,
    default: () => []
  },
  skills: {
    type: Array,
    required: true,
    default: () => []
  },
  data: {
    type: Array,
    required: true,
    default: () => []
  }
});

// Process data for the heatmap
const series = computed(() => {
  if (!props.users.length || !props.skills.length || !props.data.length) {
    return [];
  }

  // Group data by user
  const userMap = new Map();

  // Initialize with all users
  props.users.forEach(user => {
    userMap.set(user.id, {
      name: user.display_name || user.name || user.external_id || `User ${user.id}`,
      data: []
    });
  });

  // Create a map of skill IDs to names for quick lookup
  const skillMap = new Map(props.skills.map(skill => [skill.id, skill.name]));

  // Get all unique skill names for consistent ordering
  const allSkillNames = [...new Set(props.skills.map(skill => skill.name))].sort();

  // For each user, populate their skill data
  props.data.forEach(item => {
    const userId = item.user_id;
    const userEntry = userMap.get(userId);

    if (userEntry) {
      userEntry.data.push({
        x: item.skill_name,
        y: item.accuracy_percentage || 0
      });
    }
  });

  // Ensure all users have entries for all skills (with 0 for missing skills)
  userMap.forEach((userEntry) => {
    // Get current skill names in user data
    const userSkillNames = new Set(userEntry.data.map(item => item.x));

    // Add missing skills with 0 accuracy
    allSkillNames.forEach(skillName => {
      if (!userSkillNames.has(skillName)) {
        userEntry.data.push({
          x: skillName,
          y: 0
        });
      }
    });

    // Sort data by skill name for consistent ordering
    userEntry.data.sort((a, b) => a.x.localeCompare(b.x));
  });

  // Convert map to array for ApexCharts
  return Array.from(userMap.values());
});

// Chart options
const chartOptions = computed(() => ({
  chart: {
    type: 'heatmap',
    toolbar: {
      show: true,
      tools: {
        download: true,
        selection: false,
        zoom: false,
        zoomin: false,
        zoomout: false,
        pan: false,
        reset: false
      }
    },
    background: 'transparent',
    fontFamily: "'Inter', 'Helvetica', 'Arial', sans-serif",
    animations: {
      enabled: true,
      easing: 'easeinout',
      speed: 800,
      animateGradually: {
        enabled: true,
        delay: 150
      },
      dynamicAnimation: {
        enabled: true,
        speed: 350
      }
    }
  },
  dataLabels: {
    enabled: true,
    formatter: function(val) {
      if (val === 0) return '';
      return val ? val.toFixed(0) + '%' : '';
    },
    style: {
      colors: ['#fff'],
      fontSize: '12px',
      fontWeight: 600,
      textShadow: '1px 1px 2px rgba(0,0,0,0.5)'
    }
  },
  colors: ["#008FFB"], // Blue color as specified
  title: {
    text: 'Skill Performance Heatmap',
    align: 'center',
    style: {
      color: '#fff',
      fontSize: '18px',
      fontWeight: 600
    }
  },
  subtitle: {
    text: 'User performance across different skills',
    align: 'center',
    style: {
      color: 'rgba(255, 255, 255, 0.7)',
      fontSize: '14px'
    }
  },
  plotOptions: {
    heatmap: {
      shadeIntensity: 0.5,
      radius: 3,
      useFillColorAsStroke: false,
      enableShades: true,
      distributed: false,
      colorScale: {
        ranges: [
          {
            from: 0,
            to: 0,
            name: 'No Data',
            color: '#1f2937' // Dark gray for no data
          },
          {
            from: 1,
            to: 40,
            name: 'Low',
            color: '#10b981' // Green for low performance
          },
          {
            from: 41,
            to: 70,
            name: 'Medium',
            color: '#f59e0b' // Orange/Amber for medium performance
          },
          {
            from: 71,
            to: 100,
            name: 'High',
            color: '#ef4444' // Red for high performance
          }
        ]
      }
    }
  },
  tooltip: {
    theme: 'dark',
    custom: ({ series, seriesIndex, dataPointIndex, w }) => {
      const user = w.globals.seriesNames[seriesIndex];
      const skill = w.globals.labels[dataPointIndex];
      const value = series[seriesIndex][dataPointIndex];

      // Find the corresponding data point in the original data
      const dataPoint = props.data.find(item =>
        item.user_id === props.users[seriesIndex]?.id &&
        item.skill_name === skill
      );

      const totalQuestions = dataPoint?.total_questions_answered || 0;
      const correctAnswers = dataPoint?.correct_answers || 0;

      return `
        <div class="apexcharts-tooltip-custom" style="padding: 10px; background: rgba(30, 30, 30, 0.95); border-radius: 6px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
          <div style="font-weight: bold; margin-bottom: 5px; color: #fff; font-size: 14px;">${user}</div>
          <div style="font-weight: bold; color: #008FFB; margin-bottom: 8px; font-size: 13px;">${skill}</div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <span style="color: rgba(255,255,255,0.7);">Accuracy:</span>
            <span style="color: #fff; font-weight: bold;">${value}%</span>
          </div>
          <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
            <span style="color: rgba(255,255,255,0.7);">Questions:</span>
            <span style="color: #fff;">${totalQuestions}</span>
          </div>
          <div style="display: flex; justify-content: space-between;">
            <span style="color: rgba(255,255,255,0.7);">Correct:</span>
            <span style="color: #10b981;">${correctAnswers}</span>
          </div>
        </div>
      `;
    }
  },
  xaxis: {
    labels: {
      style: {
        colors: '#fff',
        fontSize: '12px'
      },
      trim: true,
      maxHeight: 120,
      rotate: -45
    },
    tooltip: {
      enabled: false
    }
  },
  yaxis: {
    labels: {
      style: {
        colors: '#fff',
        fontSize: '12px'
      }
    }
  },
  legend: {
    position: 'bottom',
    horizontalAlign: 'center',
    labels: {
      colors: '#fff'
    },
    markers: {
      radius: 3
    }
  },
  grid: {
    borderColor: 'rgba(255, 255, 255, 0.1)',
    padding: {
      right: 10,
      left: 10
    }
  },
  responsive: [
    {
      breakpoint: 768,
      options: {
        chart: {
          height: 350
        },
        xaxis: {
          labels: {
            rotate: -90
          }
        }
      }
    }
  ]
}));
</script>
