<script setup>
import { ref, computed } from 'vue';
import { cn } from '@/lib/utils';

const props = defineProps({
  size: {
    type: Number,
    default: 28
  },
  class: {
    type: String,
    default: ''
  }
});

const isAnimating = ref(false);
const isControlled = ref(false);

// Animation states for the paths
const pathVariants = {
  normal: 'M5 12h14',
  animate: ['M5 12h14', 'M5 12h9', 'M5 12h14']
};

const secondaryPathVariants = {
  normal: 'm12 5 7 7-7 7',
  animate: 'm12 5 7 7-7 7'
};

// Computed path values based on animation state
const mainPath = computed(() => {
  return isAnimating.value ? pathVariants.animate[1] : pathVariants.normal;
});

const secondaryPath = computed(() => {
  return secondaryPathVariants.normal;
});

function startAnimation() {
  isControlled.value = true;
  isAnimating.value = true;
}

function stopAnimation() {
  isControlled.value = true;
  isAnimating.value = false;
}

function handleMouseEnter() {
  if (!isControlled.value) {
    isAnimating.value = true;
  }
}

function handleMouseLeave() {
  if (!isControlled.value) {
    isAnimating.value = false;
  }
}

defineExpose({
  startAnimation,
  stopAnimation
});
</script>

<template>
  <div
    :class="cn(
      'select-none flex items-center justify-center cursor-pointer',
      props.class
    )"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
  >
    <svg
      xmlns="http://www.w3.org/2000/svg"
      :width="size"
      :height="size"
      :style="{width: size + 'px', height: size + 'px'}"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      stroke-width="2"
      stroke-linecap="round"
      stroke-linejoin="round"
      class="arrow-icon"
    >
      <path
        :d="mainPath"
        :class="[isAnimating ? 'animate-main-path' : '']"
      />
      <path
        :d="secondaryPath"
        :class="[isAnimating ? 'animate-secondary-path' : '']"
      />
    </svg>
  </div>
</template>

<style scoped>
.animate-main-path {
  animation: main-path-animation 0.4s ease-in-out;
}

.animate-secondary-path {
  animation: secondary-path-animation 0.4s ease-in-out;
}

@keyframes main-path-animation {
  0% {
    stroke-dasharray: 14;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 9;
    stroke-dashoffset: -2;
  }
  100% {
    stroke-dasharray: 14;
    stroke-dashoffset: 0;
  }
}

@keyframes secondary-path-animation {
  0% { transform: translateX(0); }
  50% { transform: translateX(-3px); }
  100% { transform: translateX(0); }
}

.arrow-icon {
  transition: all 0.3s ease;
}

.arrow-icon:hover {
  transform: scale(1.05);
}
</style>
