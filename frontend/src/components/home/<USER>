<template>
  <div class="herbit-art-container">
    <!-- ASCII Art Display -->
    <div class="relative mx-auto max-w-6xl animate-float shadow-glow-lg rounded-xl overflow-hidden border border-white/10">
      <div class="bg-phantom-dark rounded-t-xl border-b border-white/10 p-3 flex items-center">
        <div class="flex space-x-2">
          <div class="w-3 h-3 rounded-full bg-red-500"></div>
          <div class="w-3 h-3 rounded-full bg-yellow-500"></div>
          <div class="w-3 h-3 rounded-full bg-green-500"></div>
        </div>
        <div class="mx-auto text-white/60 text-xs">H E R B I T</div>
      </div>
      <div class="bg-gradient-to-br from-phantom-dark to-phantom-dark-blue p-12 flex justify-center">
        <!-- Character Typewriter Effect -->
        <pre
          :key="'char-typewriter-' + animationKey"
          class="herbit-char-typewriter text-cyan-300 font-mono text-base leading-relaxed whitespace-pre select-none"
          style="font-family: 'Courier New', Consolas, monospace;"
          v-html="getCharTypewriterHTML()"
        ></pre>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const herbitArt = `___________
 |           \\
  |            \\
         _   _   _____  |   __  __    \\  ____    _   _______
         | | | | |  ___| |  |__||__|   / |  _ \\  | | |__   __|
      | |_| | | |_    |     __     /  | |_) | | |    | |
      |  _  | |  _|   |           /   |  _ <  | |    | |
      | | | | | |___  |     |\\    \\   | |_) | | |    | |
      |_| |_| |_____| |     | \\    \\  |____/  |_|    |_|
   |     |  \\    \\
    |     |   \\    \\
     |_____|    \\____\\

     Highly Engineered Robot Built for Internal Tests`

const animationKey = ref(0)
const currentAnimation = 'spiral'
const animationSpeed = 0.02
let autoReplayInterval = null

const getCharTypewriterHTML = () => {
  const lines = herbitArt.split('\n')
  let result = ''
  let charIndex = 0

  // Define the positions that form the 'R' shape in the ASCII art
  const rPositions = [
    [0, [0,1,2,3,4,5,6,7,8,9,10]],
    [1, [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14]],
    [2, [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15]],
    [3, [24,25,26,27,28,29,30,31,32,33,34,35,36,37,38]],
    [4, [25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40]],
    [5, [22,24,25,26,27,28,29,30,31,32,33,34,35,36]],
    [6, [22,23,24,25,26,27,28,29,30,31,32,33,34,35]],
    [7, [22,23,24,25,26,27,28,29,30,31,32,33,34,35]],
    [8, [22,23,24,25,26,27,28,29,30,31,32,33,34,35]],
    [9, [0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19]],
    [10,[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19]],
    [11,[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]]
  ]

  // Process each line
  for (let rowIndex = 0; rowIndex < lines.length; rowIndex++) {
    const line = lines[rowIndex]

    // Process each character in the line
    for (let colIndex = 0; colIndex < line.length; colIndex++) {
      const char = line[colIndex]

      // Check if this position is part of the R shape
      const isRPosition = rPositions.some(pos => {
        const [row, cols] = pos
        return row === rowIndex && cols.includes(colIndex)
      })

      if (char === ' ') {
        result += '&nbsp;'
      } else if (char === '\n') {
        result += '<br>'
      } else {
        const delay = getSpiralDelay(rowIndex, colIndex)
        const color = isRPosition ? '#67e8f9' : 'white'

        result += `<span class="char anim-spiral" style="animation-delay: ${delay}s; color: ${color}; font-weight: bold;">${char}</span>`
      }
      charIndex++
    }

    // Add line break at the end of each line except the last one
    if (rowIndex < lines.length - 1) {
      result += '<br>'
    }
  }

  return result
}

const getSpiralDelay = (rowIndex, colIndex) => {
  const centerRow = 6
  const centerCol = 20
  const distance = Math.sqrt(Math.pow(rowIndex - centerRow, 2) + Math.pow(colIndex - centerCol, 2))
  return distance * animationSpeed
}

const resetAnimation = () => {
  animationKey.value++
}

// Auto-replay every 1 minute (60000ms)
onMounted(() => {
  autoReplayInterval = setInterval(() => {
    resetAnimation()
  }, 60000)
})

// Clean up interval when component is unmounted
onUnmounted(() => {
  if (autoReplayInterval) {
    clearInterval(autoReplayInterval)
  }
})
</script>

<style scoped>
.herbit-art-container {
  @apply w-full;
}

/* Animation Classes */
.char {
  opacity: 0;
  transform: scale(0.8);
  display: inline-block;
}

/* Spiral Effect */
.anim-spiral {
  animation: spiral 1s ease-out forwards;
}

@keyframes spiral {
  0% {
    opacity: 0;
    transform: rotate(180deg) scale(0.5);
  }
  50% {
    opacity: 0.7;
    transform: rotate(90deg) scale(1.1);
  }
  100% {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* Additional responsive adjustments */
@media (max-width: 768px) {
  .herbit-art-container pre {
    font-size: 0.75rem;
    line-height: 1.2;
  }
}

@media (max-width: 640px) {
  .herbit-art-container pre {
    font-size: 0.625rem;
    line-height: 1.1;
  }
}
</style>
