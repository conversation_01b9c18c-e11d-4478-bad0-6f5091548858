import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './assets/css/main.css'
import './assets/css/theme.css'

import '@fontsource/fira-code/400.css'
import '@fontsource/fira-code/700.css'

// Import ApexCharts
import VueApexCharts from 'vue3-apexcharts'

// Import SVG Icon component
import SvgIcon from './components/SvgIcon.vue'

// Import logging utilities
import { error, critical } from './utils/logger'

// Global error handler for uncaught errors
window.addEventListener('error', (event) => {
  critical('Global error caught:', { error: event.error, filename: event.filename, lineno: event.lineno });
});

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  critical('Unhandled promise rejection:', { reason: event.reason });
});

try {
  const app = createApp(App);

  // Add global error handler for Vue errors
  app.config.errorHandler = (err, instance, info) => {
    error('Vue error caught:', { error: err.message, info, stack: err.stack });
  };

  app.use(router);
  app.use(VueApexCharts);

  // Register SvgIcon component globally
  app.component('SvgIcon', SvgIcon);

  // Ensure DOM is ready before mounting
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      const appElement = document.getElementById('app');
      if (appElement) {
        app.mount('#app');
      } else {
        critical('App element not found in DOM');
      }
    });
  } else {
    const appElement = document.getElementById('app');
    if (appElement) {
      app.mount('#app');
    } else {
      critical('App element not found in DOM');
    }
  }
} catch (appError) {
  critical('Failed to initialize Vue app:', { error: appError.message, stack: appError.stack });
}
