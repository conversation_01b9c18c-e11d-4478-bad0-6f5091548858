<template>
  <div class="h-screen overflow-hidden">
    <!-- Main Content -->
    <main class="h-full overflow-hidden">
      <!-- Hero Section -->
      <PhantomHero />
    </main>
  </div>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import PhantomHero from "../components/layout/Hero.vue";

// Disable page scrolling when component mounts
onMounted(() => {
  document.body.style.overflow = "hidden";
  document.documentElement.style.overflow = "hidden";
  document.body.classList.add("no-scroll-active");
});

// Re-enable page scrolling when component unmounts
onUnmounted(() => {
  document.body.style.overflow = "";
  document.documentElement.style.overflow = "";
  document.body.classList.remove("no-scroll-active");
});
</script>
