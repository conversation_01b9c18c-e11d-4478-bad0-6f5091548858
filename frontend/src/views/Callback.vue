<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="loading-message text-center p-8 bg-gray-900/70 backdrop-blur-md rounded-xl border border-cyan-500/30 shadow-glow-md">
      <div class="flex flex-col items-center">
        <!-- Loading spinner -->
        <svg class="animate-spin h-10 w-10 text-cyan-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <h2 class="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2">
          HERBIT
        </h2>
        <p class="text-gray-300">Processing authentication...</p>
        <p class="text-gray-400 text-sm mt-2">You will be redirected shortly</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { debug, info, warning, error, logUserAction } from '@/utils/logger'

const router = useRouter()

onMounted(async () => {
  const params = new URLSearchParams(window.location.search)
  const code = params.get('code')
  const receivedState = params.get('state')
  const storedState = localStorage.getItem('oauth_state')

  debug('Full URL:', window.location.href)
  debug('Authorization code:', code)
  debug('Code length:', code ? code.length : 0)
  debug('Received state:', { receivedState })
  debug('Stored state:', { storedState })

  // Clear the stored state to prevent reuse
  localStorage.removeItem('oauth_state')

  if (!code) {
    warning('No code in URL, redirecting to login')
    router.push('/login')
    return
  }

  if (code.length < 20) {
    warning('Authorization code appears to be too short, this might indicate a problem')
  }

  // Check if this code has already been used
  const usedCode = localStorage.getItem('used_auth_code')
  if (usedCode === code) {
    warning('Authorization code has already been used, redirecting to login')
    router.push('/login')
    return
  }

  // Mark this code as used
  localStorage.setItem('used_auth_code', code)

  // Verify state parameter to prevent CSRF attacks
  if (!receivedState || receivedState !== storedState) {
    warning('State parameter mismatch or missing', { receivedState, storedState })
    // We'll continue anyway since we're using a direct token exchange on the backend
    // that doesn't rely on the state parameter for security
  }

  const data = {
    code: code,
    redirect_uri: import.meta.env.VITE_AUTH_CALLBACK_URL,
    state: receivedState // Include the state in the token request
  }

  try {
    // Use relative URL so it goes through Vite proxy
    const response = await fetch('/auth/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data),
      credentials: 'include' // Include cookies for session handling
    })

    if (response.ok) {
      const result = await response.json()
      debug('Token exchange successful:', { result })

      // Authentication successful - fetch user info from session
      try {
        // Use relative URL so it goes through Vite proxy
        const userInfoResponse = await fetch('/auth/userinfo', {
          method: 'GET',
          credentials: 'include'
        });

        if (userInfoResponse.ok) {
          const userInfoData = await userInfoResponse.json();
          if (userInfoData.authenticated && userInfoData.user) {
            localStorage.setItem('user_info', JSON.stringify(userInfoData.user));
            debug('User info fetched from session:', { user: userInfoData.user })
          }
        }
      } catch (userInfoError) {
        error('Error fetching user info:', { error: userInfoError });
      }

      // Log user groups for debugging
      const userInfoStr = localStorage.getItem('user_info');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          debug('User info in callback:', { userInfo });

          if (userInfo.groups) {
            // Get group names from environment variables
            const adminGroupName = import.meta.env.VITE_ADMIN_GROUP_NAME || 'admins';
            const employeeGroupName = import.meta.env.VITE_EMPLOYEE_GROUP_NAME || 'employees';

            debug('User groups analysis:', {
              groups: userInfo.groups,
              isAdmin: userInfo.groups.includes(adminGroupName),
              isEmployee: userInfo.groups.includes(employeeGroupName)
            });
          }
        } catch (e) {
          error('Error parsing user info:', { error: e });
        }
      }

      // Trigger a localStorage event to notify other components about the authentication change
      // This is needed because the storage event only fires when localStorage is changed from another tab
      window.dispatchEvent(new Event('auth-state-changed'));

      // Clear the used code after successful authentication
      localStorage.removeItem('used_auth_code')

      // Redirect to root path - the DynamicHome component will handle showing the appropriate view
      info('Authentication successful, redirecting to home');
      logUserAction('authentication_success');
      router.push('/')
    } else {
      const errorResult = await response.json()
      error('Authentication failed:', { status: response.status, statusText: response.statusText, errorResult })
      logUserAction('authentication_failed', { status: response.status, errorResult });
      router.push('/error') // Redirect to error page
    }
  } catch (err) {
    error('Network or server error during authentication:', { error: err })
    logUserAction('authentication_error', { error: err.message });
    router.push('/error') // Redirect to error page
  }
})
</script>

<style scoped>
.callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-message {
  text-align: center;
  font-size: 1.2rem;
}
</style>
