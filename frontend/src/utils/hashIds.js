/**
 * HashID utilities for frontend
 * Handles working with hashed IDs from the API and provides decoding functionality
 */

import Hashids from 'hashids'

// Configuration matching the Python backend
const BASE_SALT = 'herbit_quiz_system'
const MIN_LENGTH = 8
const ALPHABET = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890'
const TYPE_MAPPING = {
  'assessment': 1,
  'skill': 2,
  'session': 3
}

/**
 * Generate dynamic salt using SHA-256
 * @returns {string} - The generated salt
 */
async function generateDynamicSalt() {
  // Use Web Crypto API to generate SHA-256 hash
  const encoder = new TextEncoder()
  const data = encoder.encode(BASE_SALT)
  const hash = await crypto.subtle.digest('SHA-256', data)

  // Convert to hex string and take first 16 characters
  const hashArray = Array.from(new Uint8Array(hash))
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  return hashHex.substring(0, 16)
}

/**
 * Get Hashids instance with the same configuration as Python backend
 * @returns {Promise<Hashids>} - Configured Hashids instance
 */
async function getHashidsInstance() {
  const salt = await generateDynamicSalt()
  return new Hashids(salt, MIN_LENGTH, ALPHABET)
}

/**
 * Decode a hash string to get the original entity ID
 * @param {string} hashString - The hash to decode
 * @param {string} entityType - The expected entity type ('assessment', 'skill', 'session')
 * @returns {Promise<number|null>} - The decoded ID or null if invalid
 */
export async function decodeHashId(hashString, entityType) {
  if (!hashString || typeof hashString !== 'string') {
    return null
  }

  const expectedType = TYPE_MAPPING[entityType]
  if (expectedType === undefined) {
    // Import logger inline to avoid circular dependencies
    import('@/utils/logger').then(({ warning }) => {
      warning(`Invalid entity type: ${entityType}`);
    });
    return null
  }

  try {
    const hashids = await getHashidsInstance()
    const decoded = hashids.decode(hashString)

    if (decoded && decoded.length >= 2) {
      const [entityId, typeNumber] = decoded
      if (typeNumber === expectedType) {
        // Import logger inline to avoid circular dependencies
        import('@/utils/logger').then(({ debug }) => {
          debug(`Successfully decoded ${entityType} hash ${hashString} to ID ${entityId}`);
        });
        return entityId
      } else {
        // It's a valid hash but wrong type
        const actualType = Object.keys(TYPE_MAPPING).find(key => TYPE_MAPPING[key] === typeNumber) || 'unknown'
        import('@/utils/logger').then(({ debug }) => {
          debug(`Hash ${hashString} is a valid ${actualType} hash, not a ${entityType} hash`);
        });
        return null
      }
    }
  } catch (error) {
    import('@/utils/logger').then(({ debug }) => {
      debug(`Failed to decode hash ${hashString}:`, { error: error.message });
    });
  }

  import('@/utils/logger').then(({ warning }) => {
    warning(`Failed to decode ${entityType} hash: ${hashString}`);
  });
  return null
}

/**
 * Decode assessment hash to get assessment ID
 * @param {string} hashString - The assessment hash
 * @returns {Promise<number|null>} - The assessment ID or null
 */
export async function decodeAssessmentId(hashString) {
  return await decodeHashId(hashString, 'assessment')
}

/**
 * Decode skill hash to get skill ID
 * @param {string} hashString - The skill hash
 * @returns {Promise<number|null>} - The skill ID or null
 */
export async function decodeSkillId(hashString) {
  return await decodeHashId(hashString, 'skill')
}

/**
 * Decode session hash to get session ID
 * @param {string} hashString - The session hash
 * @returns {Promise<number|null>} - The session ID or null
 */
export async function decodeSessionId(hashString) {
  return await decodeHashId(hashString, 'session')
}

/**
 * Detect what type of entity a hash represents
 * @param {string} hashString - The hash to analyze
 * @returns {Promise<string|null>} - 'assessment', 'skill', 'session', or null if not decodable
 */
export async function detectHashType(hashString) {
  if (!hashString || typeof hashString !== 'string') {
    return null
  }

  try {
    const hashids = await getHashidsInstance()
    const decoded = hashids.decode(hashString)

    if (decoded && decoded.length >= 2) {
      const typeNumber = decoded[1]
      const typeMapping = { 1: 'assessment', 2: 'skill', 3: 'session' }
      return typeMapping[typeNumber] || null
    }
  } catch (error) {
    import('@/utils/logger').then(({ debug }) => {
      debug('Error detecting hash type:', { error: error.message });
    });
  }

  return null
}

/**
 * Get the hash ID from an entity object, falling back to regular ID
 * @param {Object} entity - The entity object from API
 * @param {string} type - The type of entity ('assessment', 'skill', 'session')
 * @returns {string} - The hash ID or regular ID as string
 */
export function getHashId(entity, type) {
  if (!entity) return null;

  // Try to get the hash version first
  // For most entities, the hash is stored as 'id_hash'
  if (entity.id_hash) {
    return entity.id_hash;
  }

  // Try specific hash keys for different types
  const hashKey = `${type}_id_hash`;
  if (entity[hashKey]) {
    return entity[hashKey];
  }

  // Fall back to regular ID
  const idKey = type === 'session' ? 'id' : type + '_id';
  if (entity[idKey]) {
    return String(entity[idKey]);
  }

  // Last resort - try generic 'id' field
  if (entity.id) {
    return String(entity.id);
  }

  return null;
}

/**
 * Get assessment hash ID from assessment object
 * @param {Object} assessment - Assessment object from API
 * @returns {string} - Assessment hash ID or regular ID
 */
export function getAssessmentHashId(assessment) {
  return getHashId(assessment, 'assessment');
}

/**
 * Get skill hash ID from skill object
 * @param {Object} skill - Skill object from API
 * @returns {string} - Skill hash ID or regular ID
 */
export function getSkillHashId(skill) {
  return getHashId(skill, 'skill');
}

/**
 * Get session hash ID from session object
 * @param {Object} session - Session object from API
 * @returns {string} - Session hash ID or regular ID
 */
export function getSessionHashId(session) {
  return getHashId(session, 'session');
}

/**
 * Get the decoded ID from an entity's hash
 * @param {Object} entity - The entity object from API
 * @param {string} type - The type of entity ('assessment', 'skill', 'session')
 * @returns {Promise<number|null>} - The decoded original ID
 */
export async function getDecodedId(entity, type) {
  if (!entity) return null;

  // First try to get the hash ID
  const hashId = getHashId(entity, type);
  if (!hashId) return null;

  // If it's a regular numeric ID, return it as is
  if (!isHashId(hashId)) {
    return parseInt(hashId, 10);
  }

  // Decode the hash to get the original ID
  return await decodeHashId(hashId, type);
}

/**
 * Get decoded assessment ID from assessment object
 * @param {Object} assessment - Assessment object from API
 * @returns {Promise<number|null>} - Decoded assessment ID
 */
export async function getDecodedAssessmentId(assessment) {
  return await getDecodedId(assessment, 'assessment');
}

/**
 * Get decoded skill ID from skill object
 * @param {Object} skill - Skill object from API
 * @returns {Promise<number|null>} - Decoded skill ID
 */
export async function getDecodedSkillId(skill) {
  return await getDecodedId(skill, 'skill');
}

/**
 * Get decoded session ID from session object
 * @param {Object} session - Session object from API
 * @returns {Promise<number|null>} - Decoded session ID
 */
export async function getDecodedSessionId(session) {
  return await getDecodedId(session, 'session');
}

/**
 * Get the display ID (original integer ID) from an entity
 * @param {Object} entity - The entity object from API
 * @param {string} type - The type of entity ('assessment', 'skill', 'session')
 * @returns {number|string} - The original ID for display
 */
export function getDisplayId(entity, type) {
  if (!entity) return null;

  const idKey = type === 'session' ? 'id' : type + '_id';
  if (entity[idKey]) {
    return entity[idKey];
  }

  // Fall back to generic 'id' field
  if (entity.id) {
    return entity.id;
  }

  return null;
}

/**
 * Get hashed skill IDs from a list of skill IDs
 * @param {Array} skillIds - Array of skill ID objects or integers
 * @returns {Array} - Array of hashed skill IDs
 */
export function getHashedSkillIds(skillIds) {
  if (!Array.isArray(skillIds)) return [];

  return skillIds.map(skillId => {
    if (typeof skillId === 'object' && skillId.skill_id_hash) {
      return skillId.skill_id_hash;
    } else if (typeof skillId === 'number') {
      return String(skillId);
    }
    return skillId;
  });
}

/**
 * Check if a string looks like a hash ID (contains letters)
 * @param {string} id - The ID to check
 * @returns {boolean} - True if it looks like a hash
 */
export function isHashId(id) {
  if (!id || typeof id !== 'string') return false;
  return /[a-zA-Z]/.test(id);
}

/**
 * Prepare entity data for navigation using hash IDs
 * @param {Object} entity - The entity object
 * @param {string} type - The type of entity
 * @returns {Object} - Object with both hash and display IDs
 */
export function prepareEntityForNavigation(entity, type) {
  return {
    hashId: getHashId(entity, type),
    displayId: getDisplayId(entity, type),
    entity
  };
}

/**
 * Decode a session hash to get the 6-digit session code
 * @param {string} hashString - The session hash to decode
 * @returns {Promise<string|null>} - The 6-digit session code or null if invalid
 */
export async function decodeSessionCodeFromHash(hashString) {
  if (!hashString || typeof hashString !== 'string') {
    return null
  }

  const expectedType = TYPE_MAPPING['session']

  try {
    const hashids = await getHashidsInstance()
    const decoded = hashids.decode(hashString)

    if (decoded && decoded.length >= 2) {
      const [sessionCodeInt, typeNumber] = decoded
      if (typeNumber === expectedType) {
        // Convert back to 6-digit string with leading zeros
        return sessionCodeInt.toString().padStart(6, '0')
      }
    }
  } catch (error) {
    import('@/utils/logger').then(({ debug }) => {
      debug(`Failed to decode session hash ${hashString}:`, { error: error.message });
    });
  }

  return null
}

/**
 * Get display session code from session object (synchronous version)
 * This function handles getting the session code for UI display
 * @param {Object} session - Session object from API
 * @returns {string} - The session code to display, or 'N/A' if unavailable
 */
export function getDisplaySessionCode(session) {
  if (!session) return 'N/A';

  // If session_code is still present in the response, use it directly
  if (session.session_code) {
    return session.session_code;
  }

  // If code field is present, use it
  if (session.code) {
    return session.code;
  }

  // If sessionCode is present, use it
  if (session.sessionCode) {
    return session.sessionCode;
  }

  // If the session has a cached decoded session code, use it
  if (session._decodedSessionCode) {
    return session._decodedSessionCode;
  }

  // Return 'Loading...' if we need to decode, this will trigger async decoding
  if (session.id_hash && !session._decodingAttempted) {
    return 'Loading...';
  }

  return 'N/A';
}

/**
 * Decode and cache session codes for a list of sessions
 * This modifies the session objects by adding _decodedSessionCode property
 * @param {Array} sessions - Array of session objects
 * @returns {Promise<Array>} - Array of sessions with decoded session codes
 */
export async function decodeSessionCodes(sessions) {
  if (!Array.isArray(sessions)) return sessions;

  const decodePromises = sessions.map(async (session) => {
    // Skip if already has session code or already decoded
    if (session.session_code || session.code || session.sessionCode || session._decodedSessionCode) {
      return session;
    }

    // Mark as decoding attempted
    session._decodingAttempted = true;

    // Try to decode from id_hash
    if (session.id_hash) {
      try {
        const decodedCode = await decodeSessionCodeFromHash(session.id_hash);
        session._decodedSessionCode = decodedCode || 'N/A';
      } catch (error) {
        import('@/utils/logger').then(({ warning }) => {
          warning('Failed to decode session code for session:', {
            sessionHash: session.id_hash,
            error: error.message
          });
        });
        session._decodedSessionCode = 'N/A';
      }
    } else {
      session._decodedSessionCode = 'N/A';
    }

    return session;
  });

  return Promise.all(decodePromises);
}

/**
 * Extract session code from various session object formats
 * @param {Object} session - Session object from API
 * @returns {string|null} - The session code or null if not found
 */
export function extractSessionCode(session) {
  if (!session) return null;

  // Try different possible field names
  return session.session_code ||
         session.code ||
         session.sessionCode ||
         null;
}

/**
 * Check if a session object has a valid session code
 * @param {Object} session - Session object from API
 * @returns {boolean} - True if session has a valid session code
 */
export function hasValidSessionCode(session) {
  const code = extractSessionCode(session);
  return code && typeof code === 'string' && code.length === 6 && /^\d{6}$/.test(code);
}
