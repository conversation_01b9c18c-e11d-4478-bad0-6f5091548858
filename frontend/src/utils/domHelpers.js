/**
 * DOM Helper utilities to prevent "Node cannot be found" errors
 */

// Import logger inline to avoid circular dependencies
const logWarning = (message, data) => {
  import('./logger').then(({ warning }) => {
    warning(message, data);
  });
};

/**
 * Safely query a DOM element
 * @param {string} selector - CSS selector
 * @param {Element} parent - Parent element to search within (optional)
 * @returns {Element|null} Found element or null
 */
export const safeQuerySelector = (selector, parent = document) => {
  try {
    if (!parent || typeof parent.querySelector !== 'function') {
      return null;
    }
    return parent.querySelector(selector);
  } catch (error) {
    logWarning(`Safe query selector failed for "${selector}":`, { error: error.message });
    return null;
  }
};

/**
 * Safely query multiple DOM elements
 * @param {string} selector - CSS selector
 * @param {Element} parent - Parent element to search within (optional)
 * @returns {NodeList|Array} Found elements or empty array
 */
export const safeQuerySelectorAll = (selector, parent = document) => {
  try {
    if (!parent || typeof parent.querySelectorAll !== 'function') {
      return [];
    }
    return parent.querySelectorAll(selector);
  } catch (error) {
    logWarning(`Safe query selector all failed for "${selector}":`, { error: error.message });
    return [];
  }
};

/**
 * Safely get element by ID
 * @param {string} id - Element ID
 * @returns {Element|null} Found element or null
 */
export const safeGetElementById = (id) => {
  try {
    if (!id || typeof id !== 'string') {
      return null;
    }
    return document.getElementById(id);
  } catch (error) {
    logWarning(`Safe get element by ID failed for "${id}":`, { error: error.message });
    return null;
  }
};

/**
 * Safely check if an element exists and has a specific method
 * @param {Element} element - Element to check
 * @param {string} methodName - Method name to check for
 * @returns {boolean} True if element exists and has the method
 */
export const hasMethod = (element, methodName) => {
  return element &&
         typeof element === 'object' &&
         typeof element[methodName] === 'function';
};

/**
 * Safely call a method on an element
 * @param {Element} element - Element to call method on
 * @param {string} methodName - Method name to call
 * @param {...any} args - Arguments to pass to the method
 * @returns {any} Method result or null if failed
 */
export const safeCallMethod = (element, methodName, ...args) => {
  try {
    if (hasMethod(element, methodName)) {
      return element[methodName](...args);
    }
    return null;
  } catch (error) {
    logWarning(`Safe call method failed for "${methodName}":`, { error: error.message });
    return null;
  }
};

/**
 * Safely add event listener with automatic cleanup
 * @param {Element} element - Element to add listener to
 * @param {string} event - Event name
 * @param {Function} handler - Event handler
 * @param {Object} options - Event listener options
 * @returns {Function} Cleanup function
 */
export const safeAddEventListener = (element, event, handler, options = {}) => {
  try {
    if (!element || typeof element.addEventListener !== 'function') {
      return () => {}; // Return empty cleanup function
    }

    element.addEventListener(event, handler, options);

    // Return cleanup function
    return () => {
      try {
        if (element && typeof element.removeEventListener === 'function') {
          element.removeEventListener(event, handler, options);
        }
      } catch (cleanupError) {
        logWarning('Error during event listener cleanup:', { error: cleanupError.message });
      }
    };
  } catch (error) {
    logWarning(`Safe add event listener failed for "${event}":`, { error: error.message });
    return () => {}; // Return empty cleanup function
  }
};

/**
 * Safely access a Vue ref value
 * @param {Ref} ref - Vue ref object
 * @returns {any} Ref value or null if invalid
 */
export const safeRefValue = (ref) => {
  try {
    return ref && typeof ref === 'object' && 'value' in ref ? ref.value : null;
  } catch (error) {
    logWarning('Safe ref value access failed:', { error: error.message });
    return null;
  }
};

/**
 * Safely check if a Vue ref has a specific method
 * @param {Ref} ref - Vue ref object
 * @param {string} methodName - Method name to check for
 * @returns {boolean} True if ref value exists and has the method
 */
export const refHasMethod = (ref, methodName) => {
  const value = safeRefValue(ref);
  return hasMethod(value, methodName);
};

/**
 * Safely call a method on a Vue ref value
 * @param {Ref} ref - Vue ref object
 * @param {string} methodName - Method name to call
 * @param {...any} args - Arguments to pass to the method
 * @returns {any} Method result or null if failed
 */
export const safeCallRefMethod = (ref, methodName, ...args) => {
  const value = safeRefValue(ref);
  return safeCallMethod(value, methodName, ...args);
};

/**
 * Safely copy text to clipboard with fallback
 * @param {string} text - Text to copy
 * @returns {Promise<boolean>} True if successful, false otherwise
 */
export const safeCopyToClipboard = async (text) => {
  try {
    // Try modern clipboard API first
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      await navigator.clipboard.writeText(text);
      return true;
    }

    // Fallback for older browsers
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    return successful;
  } catch (error) {
    logWarning('Safe copy to clipboard failed:', { error: error.message });
    return false;
  }
};
