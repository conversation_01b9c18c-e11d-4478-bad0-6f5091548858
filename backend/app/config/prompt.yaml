prompts:
  questions_prompt: |
    Roles:
    - Admin: Provides input (topics, number of questions, difficulty, context).
    - User: Solves the generated quiz.

    You are an expert evaluator. Generate high-quality, topic-specific questions that test real-world knowledge and problem-solving skills.

    Inputs:
    - Topics: {topics}
    - Number of Questions: {no}
    - Difficulty Level: {level}
    - Previous Questions (Context): {context}

    Requirements:
    - Do not repeat or rephrase any question from the context.
    - Cover only the given topics.
    - If difficulty level is "all", generate an equal mix of easy, intermediate, and advanced questions.
    - If a specific difficulty level is provided, generate questions only for that level.
    - Focus on practical application, not just theory.
    - Include plausible but incorrect options (distractors).
    - Randomize correct answers (a/b/c/d) without patterns or clustering.
    - Skip questions about <PERSON><PERSON> Swarm if the topic is Docker.

    Output Format:
    ```json
    [
      {{
        "Qno": <Number>,
        "Level": "<Easy/Intermediate/Advanced>",
        "Question": "<Clear and relevant question>",
        "Options": {{
          "a": "...",
          "b": "...",
          "c": "...",
          "d": "..."
        }},
        "Answer": "<a/b/c/d>"
      }},
      ...
    ]
    ```

    Notes:
    - Keep questions short, clear, and varied.
    - All questions must be new, relevant, and match the selected topics and levels.
    - Ensure the "Level" field is exactly "easy", "intermediate", or "advanced" (lowercase).
    - /no_think

  description_prompt: |
    You are an AI assistant helping to generate structured quiz content.

    I will provide a brief topic description. Rewrite it in a format that supports generating quiz questions at three levels of difficulty:

    1. **Easy** – Questions should cover basic facts, definitions, or simple use.
    2. **Intermediate** – Questions should explore understanding, reasoning, or real-world application.
    3. **Advanced** – Questions should involve complex problem-solving, deeper logic, or edge cases.

    *Important Guidelines**:
    - Be **concise** – use as few words as necessary while staying clear.
    - Avoid **repetition** across difficulty levels.
    - Ensure **complete coverage** of the topic across all three levels.
    - Divide content **logically and accurately** between Easy, Intermediate, and Advanced.
    - Use **bullet points** and structure as shown below.

    Please return your output in the following format:

    ### **Topic Description**
    <Rewrite the topic in 1–2 clear and concise sentences.>

    ### **Question Difficulty Guidelines**

    1. **Easy** – Questions should test:
      - <Insert what an easy question should cover, such as basic definitions or syntax>

    2. **Intermediate** – Questions should test:
      - <Insert what an intermediate question should cover, such as practical usage or comparisons>

    3. **Advanced** – Questions should test:
      - <Insert what an advanced question should cover, such as complex scenarios or performance analysis>
    Here is the original topic description:
    Raw Description: ${description}
