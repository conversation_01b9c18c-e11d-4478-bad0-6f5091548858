"""
HashID middleware for transforming API responses.
Handles encoding of IDs in API payloads while preserving original IDs.
"""

import logging
from typing import Any, Dict

from ...utils.hashid_utils import (
    encode_assessment_id,
    encode_session_code,
    encode_skill_id,
)

logger = logging.getLogger(__name__)


def transform_response_data(data: Any) -> Any:
    """
    Transform response data to include hashed IDs instead of original IDs.

    Args:
        data: Response data to transform

    Returns:
        Transformed data with hashed IDs replacing original IDs
    """
    if isinstance(data, dict):
        transformed = {}

        for key, value in data.items():
            # Add hashed versions for specific ID fields and skip original ID
            if key == "id" and isinstance(value, int):
                # Determine context to decide which type of ID this is
                # For sessions, we'll handle id_hash separately using session_code
                if _is_session_context(data):
                    # Skip session ID - we'll create id_hash from session_code instead
                    continue
                elif _is_assessment_context(data):
                    transformed["id_hash"] = encode_assessment_id(value)
                elif _is_skill_context(data):
                    transformed["id_hash"] = encode_skill_id(value)
                # Skip the original 'id' field - don't add it to transformed
                continue

            elif key == "assessment_id" and isinstance(value, int):
                transformed["assessment_id_hash"] = encode_assessment_id(value)
                # Skip the original assessment_id field
                continue

            elif key == "skill_id" and isinstance(value, int):
                transformed["skill_id_hash"] = encode_skill_id(value)
                # Skip the original skill_id field
                continue

            elif key == "session_id" and isinstance(value, int):
                # Convert integer session_id to 6-digit string format before encoding
                session_code = str(value).zfill(6)
                try:
                    transformed["session_id_hash"] = encode_session_code(session_code)
                except ValueError:
                    # If encoding fails, keep the original session_id
                    logger.warning(
                        f"Could not encode session_id {value} to hash, keeping original"
                    )
                    transformed[key] = value
                    continue
                # Skip the original session_id field
                continue

            elif (
                key in ["code", "session_code", "sessionCode"]
                and isinstance(value, str)
                and _is_session_context(data)
            ):
                # For session codes, create id_hash from the session code and REMOVE the original
                if len(value) == 6 and value.isdigit():
                    transformed["id_hash"] = encode_session_code(value)
                    # Remove the original session_code field for security
                    # Skip adding the original key to transformed
                else:
                    # If it's not a valid session code, just keep it as is
                    transformed[key] = value
                continue

            elif key == "sessionDbId" and isinstance(value, int):
                # For sessionDbId, we'll keep it as is for now since it's used for internal tracking
                # The main id_hash will come from session_code
                transformed[key] = value

            elif key == "skill_ids" and isinstance(value, list):
                # Handle list of skill IDs
                skill_ids_hash = []
                for skill_id in value:
                    if isinstance(skill_id, int):
                        skill_ids_hash.append(encode_skill_id(skill_id))
                    elif isinstance(skill_id, str) and skill_id.isdigit():
                        skill_ids_hash.append(encode_skill_id(int(skill_id)))
                    else:
                        skill_ids_hash.append(skill_id)
                transformed["skill_ids_hash"] = skill_ids_hash
                # Skip the original skill_ids field
                continue

            elif isinstance(value, (dict, list)):
                # Recursively transform nested data
                transformed[key] = transform_response_data(value)
            else:
                # Copy all other fields as-is
                transformed[key] = value

        return transformed

    elif isinstance(data, list):
        return [transform_response_data(item) for item in data]
    else:
        return data


def _is_assessment_context(data: Dict) -> bool:
    """Check if the data represents an assessment."""
    # Use more specific indicators that are unique to assessments
    assessment_specific_indicators = [
        "is_final",
        "question_selection_mode",
        "total_questions",
        "composition",
        "duration_minutes",
        "skills",
        "selected_questions",
        "question_stats",
    ]
    return any(key in data for key in assessment_specific_indicators)


def _is_skill_context(data: Dict) -> bool:
    """Check if the data represents a skill."""
    skill_indicators = ["name", "description"]
    # Must have skill indicators but NOT assessment indicators
    has_skill_indicators = any(key in data for key in skill_indicators)
    has_assessment_indicators = any(
        key in data
        for key in [
            "is_final",
            "question_selection_mode",
            "total_questions",
            "composition",
        ]
    )
    return has_skill_indicators and not has_assessment_indicators


def _is_session_context(data: Dict) -> bool:
    """Check if the data represents a session."""
    # Strong indicators that this is session data
    strong_session_indicators = ["session_code", "sessionCode", "sessionDbId"]

    # Check for strong session indicators first
    has_strong_indicator = any(key in data for key in strong_session_indicators)

    # Check for session-specific combination: has assessment_id + username + status
    # (sessions reference assessments and have users)
    has_assessment_ref = "assessment_id" in data
    has_user_ref = "username" in data
    has_status = "status" in data
    has_session_combination = has_assessment_ref and has_user_ref and has_status

    result = has_strong_indicator or has_session_combination

    # Debug logging
    if logger.isEnabledFor(logging.DEBUG):
        logger.debug(f"Session context check for data keys: {list(data.keys())}")
        logger.debug(f"Has strong indicator: {has_strong_indicator}")
        logger.debug(f"Has session combination: {has_session_combination}")
        logger.debug(f"Result: {result}")

    return result


def hash_ids_in_response(data: Any) -> Any:
    """
    Main function to transform response data with hashed IDs.

    Args:
        data: Response data to transform

    Returns:
        Transformed data with hashed IDs
    """
    try:
        return transform_response_data(data)
    except Exception as e:
        logger.error(f"Error transforming response data: {str(e)}")
        # Return original data if transformation fails
        return data
