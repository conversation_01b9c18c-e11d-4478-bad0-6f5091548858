"""
Database helper functions for common database operations.
Provides a consistent interface for database interactions.
"""

import logging
from contextlib import contextmanager
from typing import Any, Dict, List, Tuple, Union

import psycopg2
import psycopg2.extras
from app.config.db_config import get_connection_pool

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@contextmanager
def get_connection():
    """Context manager for getting a connection from the pool"""
    pool = get_connection_pool()
    conn = pool.getconn()
    try:
        yield conn
    finally:
        pool.putconn(conn)


@contextmanager
def get_cursor(conn=None, cursor_factory=None):
    """Context manager for getting a cursor, optionally with a specific factory"""
    close_conn = False
    if conn is None:
        close_conn = True
        conn = get_connection_pool().getconn()

    try:
        if cursor_factory:
            cur = conn.cursor(cursor_factory=cursor_factory)
        else:
            cur = conn.cursor()
        yield cur
    finally:
        if cur:
            cur.close()
        if close_conn and conn:
            get_connection_pool().putconn(conn)


def execute_query(
    query: str, params: tuple = None, fetch: str = None, cursor_factory=None
) -> Union[List[Dict], List[Tuple], None]:
    """
    Execute a database query with parameters and return results based on fetch type.

    Args:
        query: SQL query string
        params: Query parameters
        fetch: Type of fetch operation ('all', 'one', 'many', None)
        cursor_factory: Optional cursor factory

    Returns:
        Query results based on fetch type or None for operations without results
    """
    try:
        with get_connection() as conn:
            with get_cursor(conn, cursor_factory) as cur:
                cur.execute(query, params or ())

                if fetch == "all":
                    return cur.fetchall()
                elif fetch == "one":
                    return cur.fetchone()
                elif fetch == "many":
                    return cur.fetchmany()
                else:
                    conn.commit()
                    return None
    except Exception as e:
        logger.error(f"Database error in execute_query: {e}")
        raise


def execute_batch(query: str, params_list: List[tuple]) -> None:
    """
    Execute a batch operation with multiple parameter sets.

    Args:
        query: SQL query string
        params_list: List of parameter tuples
    """
    try:
        with get_connection() as conn:
            with get_cursor(conn) as cur:
                psycopg2.extras.execute_batch(cur, query, params_list)
                conn.commit()
    except Exception as e:
        logger.error(f"Database error in execute_batch: {e}")
        raise


def fetch_as_dict(query: str, params: tuple = None) -> List[Dict[str, Any]]:
    """
    Execute a query and return results as a list of dictionaries.

    Args:
        query: SQL query string
        params: Query parameters

    Returns:
        List of dictionaries with column names as keys
    """
    try:
        results = execute_query(
            query, params, fetch="all", cursor_factory=psycopg2.extras.DictCursor
        )
        return [dict(row) for row in results] if results else []
    except Exception as e:
        logger.error(f"Database error in fetch_as_dict: {e}")
        return []
