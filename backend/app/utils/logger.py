"""
Centralized Logging Utility Module

This module provides a centralized logging system with environment-based configuration.
It replaces console.log and print statements with structured logging functions.

Features:
- Environment-based log levels (DEBUG, INFO, WARNING, ERROR)
- Structured logging with consistent formatting
- Separate log files for different log levels
- Context-aware logging with request tracking
- Performance logging capabilities
"""

import logging
import os
import sys
from pathlib import Path
from typing import Any, Dict, Optional

from dotenv import load_dotenv

load_dotenv()


class ContextFilter(logging.Filter):
    """Filter to add context information to log records."""

    def __init__(self):
        super().__init__()
        self.context = {}

    def filter(self, record):
        """Add context information to the log record."""
        for key, value in self.context.items():
            setattr(record, key, value)
        return True

    def set_context(self, **kwargs):
        """Set context information for logging."""
        self.context.update(kwargs)

    def clear_context(self):
        """Clear all context information."""
        self.context.clear()


class HerbitLogger:
    """Centralized logger for the Herbit application."""

    def __init__(self):
        self.context_filter = ContextFilter()
        self._setup_logging()

    def _setup_logging(self):
        """Set up logging configuration based on environment variables."""
        # Get log level from environment (INFO, ERROR, WARNING, DEBUG)
        log_level = os.getenv("LOG_LEVEL", "INFO").upper()
        debug_enabled = os.getenv("DEBUG", "false").lower() == "true"

        # If DEBUG is true, override log level to DEBUG
        if debug_enabled:
            log_level = "DEBUG"

        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)

        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level, logging.INFO))

        # Clear existing handlers
        root_logger.handlers.clear()

        # Create formatters
        detailed_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s"
        )

        simple_formatter = logging.Formatter(
            "%(asctime)s - %(levelname)s - %(message)s"
        )

        # Console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level, logging.INFO))
        console_handler.setFormatter(
            simple_formatter if not debug_enabled else detailed_formatter
        )
        console_handler.addFilter(self.context_filter)

        # File handlers - always create these for production logging
        # All logs file
        all_logs_handler = logging.FileHandler(log_dir / "herbit.log")
        all_logs_handler.setLevel(logging.DEBUG)
        all_logs_handler.setFormatter(detailed_formatter)
        all_logs_handler.addFilter(self.context_filter)

        # Error logs file (separate for easy monitoring)
        error_handler = logging.FileHandler(log_dir / "error.log")
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        error_handler.addFilter(self.context_filter)

        # Add handlers
        root_logger.addHandler(console_handler)
        root_logger.addHandler(all_logs_handler)
        root_logger.addHandler(error_handler)

        # Set up specific loggers
        self.logger = logging.getLogger("herbit")
        self.api_logger = logging.getLogger("herbit.api")
        self.db_logger = logging.getLogger("herbit.database")
        self.auth_logger = logging.getLogger("herbit.auth")
        self.quiz_logger = logging.getLogger("herbit.quiz")

    def set_context(self, **kwargs):
        """Set context for all subsequent log messages."""
        self.context_filter.set_context(**kwargs)

    def clear_context(self):
        """Clear logging context."""
        self.context_filter.clear_context()

    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log debug message."""
        self._log(logging.DEBUG, message, extra, **kwargs)

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log info message."""
        self._log(logging.INFO, message, extra, **kwargs)

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log warning message."""
        self._log(logging.WARNING, message, extra, **kwargs)

    def warn(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Alias for warning method."""
        self.warning(message, extra, **kwargs)

    def error(
        self,
        message: str,
        extra: Optional[Dict[str, Any]] = None,
        exception: Optional[Exception] = None,
        **kwargs,
    ):
        """Log error message."""
        if exception:
            self.logger.error(
                f"{message} - Exception: {str(exception)}",
                extra=extra,
                exc_info=True,
                **kwargs,
            )
        else:
            self._log(logging.ERROR, message, extra, **kwargs)

    def critical(self, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs):
        """Log critical message."""
        self._log(logging.CRITICAL, message, extra, **kwargs)

    def _log(
        self, level: int, message: str, extra: Optional[Dict[str, Any]] = None, **kwargs
    ):
        """Internal logging method."""
        if extra:
            message = f"{message} - Extra: {extra}"
        if kwargs:
            message = f"{message} - Context: {kwargs}"
        self.logger.log(level, message)

    # Specialized logging methods
    def log_api_request(
        self,
        method: str,
        endpoint: str,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None,
        **kwargs,
    ):
        """Log API request."""
        context = {
            "method": method,
            "endpoint": endpoint,
            "user_id": user_id,
            "request_id": request_id,
            **kwargs,
        }
        self.api_logger.info(f"API Request: {method} {endpoint}", extra=context)

    def log_api_response(
        self,
        method: str,
        endpoint: str,
        status_code: int,
        response_time: Optional[float] = None,
        user_id: Optional[str] = None,
        request_id: Optional[str] = None,
        **kwargs,
    ):
        """Log API response."""
        context = {
            "method": method,
            "endpoint": endpoint,
            "status_code": status_code,
            "response_time": response_time,
            "user_id": user_id,
            "request_id": request_id,
            **kwargs,
        }
        level = logging.ERROR if status_code >= 400 else logging.INFO
        self.api_logger.log(
            level, f"API Response: {method} {endpoint} - {status_code}", extra=context
        )

    def log_database_operation(
        self,
        operation: str,
        table: str,
        duration: Optional[float] = None,
        affected_rows: Optional[int] = None,
        **kwargs,
    ):
        """Log database operation."""
        context = {
            "operation": operation,
            "table": table,
            "duration": duration,
            "affected_rows": affected_rows,
            **kwargs,
        }
        self.db_logger.info(f"DB Operation: {operation} on {table}", extra=context)

    def log_database_error(
        self, operation: str, table: str, error: Exception, **kwargs
    ):
        """Log database error."""
        context = {
            "operation": operation,
            "table": table,
            "error": str(error),
            **kwargs,
        }
        self.db_logger.error(
            f"DB Error: {operation} on {table} failed", extra=context, exc_info=True
        )

    def log_auth_event(
        self, event: str, user_id: Optional[str] = None, success: bool = True, **kwargs
    ):
        """Log authentication event."""
        context = {"event": event, "user_id": user_id, "success": success, **kwargs}
        level = logging.INFO if success else logging.WARNING
        self.auth_logger.log(level, f"Auth Event: {event}", extra=context)

    def log_quiz_event(
        self,
        event: str,
        quiz_name: Optional[str] = None,
        user_id: Optional[str] = None,
        **kwargs,
    ):
        """Log quiz-related event."""
        context = {"event": event, "quiz_name": quiz_name, "user_id": user_id, **kwargs}
        self.quiz_logger.info(f"Quiz Event: {event}", extra=context)

    def log_performance(self, operation: str, duration: float, **kwargs):
        """Log performance metrics."""
        context = {"operation": operation, "duration": duration, **kwargs}
        level = logging.WARNING if duration > 5.0 else logging.INFO
        self.logger.log(
            level, f"Performance: {operation} took {duration:.2f}s", extra=context
        )


# Global logger instance
logger = HerbitLogger()


# Convenience functions for backward compatibility and ease of use
def debug(message: str, **kwargs):
    """Log debug message."""
    logger.debug(message, **kwargs)


def info(message: str, **kwargs):
    """Log info message."""
    logger.info(message, **kwargs)


def warning(message: str, **kwargs):
    """Log warning message."""
    logger.warning(message, **kwargs)


def warn(message: str, **kwargs):
    """Log warning message (alias)."""
    logger.warning(message, **kwargs)


def error(message: str, exception: Optional[Exception] = None, **kwargs):
    """Log error message."""
    logger.error(message, exception=exception, **kwargs)


def critical(message: str, **kwargs):
    """Log critical message."""
    logger.critical(message, **kwargs)


# Specialized logging functions
def log_api_request(method: str, endpoint: str, **kwargs):
    """Log API request."""
    logger.log_api_request(method, endpoint, **kwargs)


def log_api_response(method: str, endpoint: str, status_code: int, **kwargs):
    """Log API response."""
    logger.log_api_response(method, endpoint, status_code, **kwargs)


def log_database_operation(operation: str, table: str, **kwargs):
    """Log database operation."""
    logger.log_database_operation(operation, table, **kwargs)


def log_database_error(operation: str, table: str, error: Exception, **kwargs):
    """Log database error."""
    logger.log_database_error(operation, table, error, **kwargs)


def log_auth_event(event: str, **kwargs):
    """Log authentication event."""
    logger.log_auth_event(event, **kwargs)


def log_quiz_event(event: str, **kwargs):
    """Log quiz event."""
    logger.log_quiz_event(event, **kwargs)


def log_performance(operation: str, duration: float, **kwargs):
    """Log performance metrics."""
    logger.log_performance(operation, duration, **kwargs)


def set_context(**kwargs):
    """Set logging context."""
    logger.set_context(**kwargs)


def clear_context():
    """Clear logging context."""
    logger.clear_context()
