# Dockerfile for the Unified Question Generation Service
# Combines Worker, DLQ Monitor, and Request Tracker into a single service
FROM python:3.11-slim

# Set working directory
WORKDIR /app

ENV PYTHONPATH=/app


# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    postgresql-client \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Expose the unified service port
EXPOSE 8001

# Run the unified service (worker + dlq-monitor + request-tracker)
CMD ["python", "worker.py"]
