"""
Unified Background Worker Service
Combines Question Generation Worker, DLQ Monitor, and Request Tracker into a single service.
Handles question generation tasks, monitors failed tasks, and tracks request analytics.
"""

import asyncio
import json
import logging
import os
import time
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Any, Dict, Optional

import psycopg2
import psycopg2.extras
from fastapi import FastAPI, HTTPException, Query, Request
from pydantic import BaseModel

try:
    # Try relative import first (when run as module)
    from app.config.db_config import DATABASE_CONFIG
    from app.services.create_quiz_questions import ask_for_question
except ImportError:
    # Fallback to absolute import (when run as standalone script)
    import sys

    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    from app.config.db_config import DATABASE_CONFIG
    from app.services.create_quiz_questions import ask_for_question

# Set up logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Create FastAPI app for the unified worker
app = FastAPI(
    title="Unified Question Generation Service",
    description="Combined worker for question generation, DLQ monitoring, and request tracking",
    version="1.0.0",
)


# ============================================================================
# MODELS
# ============================================================================


class QuestionGenerationTask(BaseModel):
    """Model for question generation task data"""

    skill_id: int
    skill_name: str
    skill_description: str
    task_id: str = None  # Optional task identifier for tracking


class FailedTask(BaseModel):
    """Model for failed task data"""

    skill_id: int
    skill_name: str
    skill_description: str
    task_id: str = None
    failure_reason: str = None
    retry_count: int = 0


class RequestLog(BaseModel):
    """Model for request tracking"""

    skill_id: int
    skill_name: str
    status: str  # 'started', 'completed', 'failed', 'timeout'
    request_id: Optional[str] = None
    questions_generated: int = 0
    processing_time_seconds: float = 0.0
    error_message: Optional[str] = None


# ============================================================================
# DATABASE INITIALIZATION
# ============================================================================


async def init_all_tables():
    """Initialize all required tables for tracking and monitoring"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create request tracking table
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS question_generation_requests (
                        id SERIAL PRIMARY KEY,
                        skill_id INTEGER,
                        skill_name VARCHAR(255),
                        status VARCHAR(50),
                        request_id VARCHAR(255),
                        questions_generated INTEGER DEFAULT 0,
                        processing_time_seconds FLOAT DEFAULT 0.0,
                        error_message TEXT,
                        started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        completed_at TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Create failed tasks table
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS failed_question_tasks (
                        id SERIAL PRIMARY KEY,
                        skill_id INTEGER,
                        skill_name VARCHAR(255),
                        skill_description TEXT,
                        task_id VARCHAR(255),
                        failure_reason TEXT,
                        retry_count INTEGER DEFAULT 0,
                        failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        raw_data JSONB
                    )
                """
                )

                # Create raw failed tasks table
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS failed_question_tasks_raw (
                        id SERIAL PRIMARY KEY,
                        raw_data JSONB,
                        error_message TEXT,
                        failed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Create indexes for better performance
                cur.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_skill_requests_skill_id
                    ON question_generation_requests(skill_id)
                """
                )

                cur.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_skill_requests_status
                    ON question_generation_requests(status)
                """
                )

                cur.execute(
                    """
                    CREATE INDEX IF NOT EXISTS idx_skill_requests_created_at
                    ON question_generation_requests(created_at)
                """
                )

                conn.commit()
                logger.info("✅ All tracking and monitoring tables initialized")

    except Exception as e:
        logger.error(f"❌ Failed to initialize tables: {e}")


@app.on_event("startup")
async def startup_event():
    """Initialize tables on startup"""
    await init_all_tables()


# ============================================================================
# QUESTION GENERATION ENDPOINTS (Original Worker)
# ============================================================================


@app.post("/generate-questions")
async def handle_question_generation(request: Request):
    """
    Dapr subscription endpoint for question generation tasks.
    This endpoint is called by Dapr when a message is published to the generate-questions topic.
    """
    try:
        # Get the message data from Dapr
        body = await request.body()
        data = json.loads(body)

        # Extract the actual task data (Dapr wraps it in 'data' field)
        task_data = data.get("data", data)
        logger.info(f"Received question generation task: {task_data}")

        # Validate the task data
        try:
            task = QuestionGenerationTask(**task_data)
        except Exception as e:
            logger.error(f"Invalid task data format: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid task data: {str(e)}")

        # Process question generation synchronously to enable proper retry handling
        # Add timeout to prevent hanging indefinitely (10 minutes should be enough)
        timeout_seconds = int(
            os.getenv("QUESTION_GENERATION_TIMEOUT", "600")
        )  # 10 minutes default

        try:
            success = await asyncio.wait_for(
                process_question_generation(task), timeout=timeout_seconds
            )

            if success:
                return {
                    "status": "completed",
                    "message": "Question generation completed successfully",
                }
            else:
                # Return 500 to trigger Dapr retries
                raise HTTPException(
                    status_code=500, detail="Question generation failed"
                )

        except asyncio.TimeoutError:
            logger.error(
                f"Question generation timed out after {timeout_seconds} seconds for skill: {task.skill_name}"
            )

            # Track timeout
            await track_request_internal(
                task.skill_id,
                task.skill_name,
                "timeout",
                str(uuid.uuid4()),
                processing_time_seconds=timeout_seconds,
                error_message=f"Timeout after {timeout_seconds} seconds",
            )

            # Update task status to "failed" if we had a tracking system
            if task.task_id:
                await update_task_status(
                    task.task_id,
                    "failed",
                    {"error": f"Timeout after {timeout_seconds} seconds"},
                )
            raise HTTPException(
                status_code=500,
                detail=f"Question generation timed out after {timeout_seconds} seconds",
            )

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse request body: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        logger.error(f"Error processing question generation task: {e}")
        # Return 500 to trigger Dapr retries
        raise HTTPException(status_code=500, detail=f"Task processing failed: {str(e)}")


async def process_question_generation(task: QuestionGenerationTask) -> bool:
    """
    Process question generation for a skill synchronously.
    Returns True if successful, False if failed.
    """
    start_time = time.time()
    request_id = str(uuid.uuid4())

    try:
        logger.info(
            f"Starting question generation for skill: {task.skill_name} (ID: {task.skill_id})"
        )

        # Track request start
        await track_request_internal(
            task.skill_id, task.skill_name, "started", request_id
        )

        # Update task status to "in_progress" if we had a tracking system
        if task.task_id:
            await update_task_status(task.task_id, "in_progress")

        # Use existing question count from database to determine how many questions exist
        question_count_before = await get_question_count_for_skill(task.skill_id)
        logger.info(
            f"Skill {task.skill_name} currently has {question_count_before} questions"
        )

        # Generate questions using the existing function
        quiz_name = f"skill_{task.skill_id}_{task.skill_name}"
        topics = task.skill_description

        result = await ask_for_question(
            quiz_name, topics, skill_id=task.skill_id, skill_name=task.skill_name
        )

        if result:
            # Get new question count
            question_count_after = await get_question_count_for_skill(task.skill_id)
            questions_generated = question_count_after - question_count_before
            processing_time = time.time() - start_time

            logger.info(
                f"Successfully generated {questions_generated} questions for skill: {task.skill_name}"
            )

            # Track successful completion
            await track_request_internal(
                task.skill_id,
                task.skill_name,
                "completed",
                request_id,
                questions_generated=questions_generated,
                processing_time_seconds=processing_time,
            )

            # Update task status to "completed" if we had a tracking system
            if task.task_id:
                await update_task_status(
                    task.task_id,
                    "completed",
                    {
                        "questions_generated": questions_generated,
                        "total_questions": question_count_after,
                    },
                )
            return True
        else:
            processing_time = time.time() - start_time
            logger.error(f"Failed to generate questions for skill: {task.skill_name}")

            # Track failure
            await track_request_internal(
                task.skill_id,
                task.skill_name,
                "failed",
                request_id,
                processing_time_seconds=processing_time,
                error_message="Question generation returned None",
            )

            # Update task status to "failed" if we had a tracking system
            if task.task_id:
                await update_task_status(
                    task.task_id,
                    "failed",
                    {"error": "Question generation returned None"},
                )
            return False

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(
            f"Error in question generation for skill {task.skill_name}: {e}",
            exc_info=True,
        )

        # Track error
        await track_request_internal(
            task.skill_id,
            task.skill_name,
            "failed",
            request_id,
            processing_time_seconds=processing_time,
            error_message=str(e),
        )

        # Update task status to "failed" if we had a tracking system
        if task.task_id:
            await update_task_status(task.task_id, "failed", {"error": str(e)})
        return False


async def get_question_count_for_skill(skill_id: int) -> int:
    """Get the current number of questions for a skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT COUNT(*) as count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                result = cur.fetchone()
                return result["count"] if result else 0
    except Exception as e:
        logger.error(f"Error getting question count for skill {skill_id}: {e}")
        return 0


async def update_task_status(
    task_id: str, status: str, metadata: Dict[str, Any] = None
):
    """
    Update task status in database (optional - implement if you want task tracking)
    For now, this is just a placeholder for future implementation
    """
    logger.info(f"Task {task_id} status updated to: {status}")
    if metadata:
        logger.info(f"Task {task_id} metadata: {metadata}")


# ============================================================================
# DLQ MONITOR ENDPOINTS
# ============================================================================


@app.post("/dlq-handler")
async def handle_dlq_message(request: Request):
    """
    Dapr subscription endpoint for dead letter queue messages.
    Logs failed tasks and stores them for monitoring.
    """
    try:
        # Get the message data from Dapr
        body = await request.body()
        data = json.loads(body)

        # Extract the actual task data
        task_data = data.get("data", data)
        logger.error(f"🚨 FAILED TASK RECEIVED IN DLQ: {task_data}")

        # Validate and parse the failed task
        try:
            failed_task = FailedTask(**task_data)
        except Exception as e:
            logger.error(f"Invalid DLQ task data format: {e}")
            # Still log the raw data for investigation
            await log_failed_task_raw(task_data, str(e))
            return {"status": "logged_raw", "message": "Invalid task format logged"}

        # Log the failed task to database
        await log_failed_task(failed_task)

        # Send alerts (you can extend this)
        await send_alert(failed_task)

        return {
            "status": "logged",
            "message": f"Failed task for skill '{failed_task.skill_name}' logged successfully",
        }

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse DLQ message body: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in DLQ message")
    except Exception as e:
        logger.error(f"Error processing DLQ message: {e}")
        raise HTTPException(status_code=500, detail=f"DLQ processing failed: {str(e)}")


async def log_failed_task(failed_task: FailedTask):
    """Log failed task to database for monitoring and analysis"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Insert failed task
                cur.execute(
                    """
                    INSERT INTO failed_question_tasks
                    (skill_id, skill_name, skill_description, task_id, failure_reason, retry_count, raw_data)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """,
                    (
                        failed_task.skill_id,
                        failed_task.skill_name,
                        failed_task.skill_description,
                        failed_task.task_id,
                        failed_task.failure_reason,
                        failed_task.retry_count,
                        json.dumps(failed_task.dict()),
                    ),
                )

                conn.commit()
                logger.info(
                    f"✅ Failed task logged to database: skill_id={failed_task.skill_id}"
                )

    except Exception as e:
        logger.error(f"❌ Failed to log failed task to database: {e}")


async def log_failed_task_raw(raw_data: Dict[str, Any], error_message: str):
    """Log raw failed task data when parsing fails"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO failed_question_tasks_raw (raw_data, error_message)
                    VALUES (%s, %s)
                """,
                    (json.dumps(raw_data), error_message),
                )

                conn.commit()
                logger.info("✅ Raw failed task data logged to database")

    except Exception as e:
        logger.error(f"❌ Failed to log raw failed task: {e}")


async def send_alert(failed_task: FailedTask):
    """Send alerts for failed tasks (extend this based on your needs)"""

    # Console Alert
    logger.error(
        f"""
    🚨🚨🚨 QUESTION GENERATION FAILED 🚨🚨🚨
    Skill ID: {failed_task.skill_id}
    Skill Name: {failed_task.skill_name}
    Task ID: {failed_task.task_id}
    Failure Reason: {failed_task.failure_reason}
    Retry Count: {failed_task.retry_count}
    Time: {datetime.now().isoformat()}
    🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨🚨
    """
    )


@app.get("/dlq-stats")
async def get_dlq_stats():
    """Get statistics about failed tasks"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total failed tasks
                cur.execute("SELECT COUNT(*) as total FROM failed_question_tasks")
                total_failed = cur.fetchone()["total"]

                # Get failed tasks by skill
                cur.execute(
                    """
                    SELECT skill_name, COUNT(*) as count
                    FROM failed_question_tasks
                    GROUP BY skill_name
                    ORDER BY count DESC
                """
                )
                failed_by_skill = cur.fetchall()

                # Get recent failures (last 24 hours)
                cur.execute(
                    """
                    SELECT COUNT(*) as count
                    FROM failed_question_tasks
                    WHERE failed_at > NOW() - INTERVAL '24 hours'
                """
                )
                recent_failures = cur.fetchone()["count"]

                return {
                    "total_failed_tasks": total_failed,
                    "recent_failures_24h": recent_failures,
                    "failed_by_skill": [dict(row) for row in failed_by_skill],
                    "timestamp": datetime.now().isoformat(),
                }

    except Exception as e:
        logger.error(f"Error getting DLQ stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get DLQ statistics")


@app.get("/dlq-recent")
async def get_recent_failures(limit: int = 10):
    """Get recent failed tasks"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT * FROM failed_question_tasks
                    ORDER BY failed_at DESC
                    LIMIT %s
                """,
                    (limit,),
                )

                failures = cur.fetchall()
                return {
                    "recent_failures": [dict(row) for row in failures],
                    "count": len(failures),
                }

    except Exception as e:
        logger.error(f"Error getting recent failures: {e}")
        raise HTTPException(status_code=500, detail="Failed to get recent failures")


# ============================================================================
# REQUEST TRACKER ENDPOINTS
# ============================================================================


@app.post("/track-request")
async def track_request(request_log: RequestLog):
    """Track a question generation request"""
    try:
        return await track_request_internal(
            request_log.skill_id,
            request_log.skill_name,
            request_log.status,
            request_log.request_id,
            request_log.questions_generated,
            request_log.processing_time_seconds,
            request_log.error_message,
        )
    except Exception as e:
        logger.error(f"❌ Failed to track request: {e}")
        raise HTTPException(
            status_code=500, detail=f"Request tracking failed: {str(e)}"
        )


async def track_request_internal(
    skill_id: int,
    skill_name: str,
    status: str,
    request_id: str,
    questions_generated: int = 0,
    processing_time_seconds: float = 0.0,
    error_message: str = None,
):
    """Internal function to track request directly in database"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                if status == "started":
                    # Insert new request
                    cur.execute(
                        """
                        INSERT INTO question_generation_requests
                        (skill_id, skill_name, status, request_id, started_at)
                        VALUES (%s, %s, %s, %s, %s)
                        RETURNING id
                    """,
                        (skill_id, skill_name, status, request_id, datetime.now()),
                    )

                    request_db_id = cur.fetchone()[0]

                else:
                    # Update existing request
                    cur.execute(
                        """
                        UPDATE question_generation_requests
                        SET status = %s,
                            questions_generated = %s,
                            processing_time_seconds = %s,
                            error_message = %s,
                            completed_at = %s
                        WHERE request_id = %s OR (skill_id = %s AND status = 'started')
                        RETURNING id
                    """,
                        (
                            status,
                            questions_generated,
                            processing_time_seconds,
                            error_message,
                            (
                                datetime.now()
                                if status in ["completed", "failed", "timeout"]
                                else None
                            ),
                            request_id,
                            skill_id,
                        ),
                    )

                    result = cur.fetchone()
                    request_db_id = result[0] if result else None

                conn.commit()

                logger.info(f"✅ Request tracked: skill_id={skill_id}, status={status}")

                return {
                    "status": "tracked",
                    "request_id": request_db_id,
                    "skill_id": skill_id,
                    "tracking_status": status,
                }

    except Exception as e:
        logger.error(f"❌ Failed to track request internally: {e}")
        raise


@app.get("/skill-requests/{skill_id}")
async def get_skill_requests(
    skill_id: int,
    limit: int = Query(10, description="Number of recent requests to return"),
    status: Optional[str] = Query(None, description="Filter by status"),
):
    """Get request history for a specific skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                query = """
                    SELECT * FROM question_generation_requests
                    WHERE skill_id = %s
                """
                params = [skill_id]

                if status:
                    query += " AND status = %s"
                    params.append(status)

                query += " ORDER BY created_at DESC LIMIT %s"
                params.append(limit)

                cur.execute(query, params)
                requests = cur.fetchall()

                return {
                    "skill_id": skill_id,
                    "requests": [dict(row) for row in requests],
                    "count": len(requests),
                }

    except Exception as e:
        logger.error(f"Error getting skill requests: {e}")
        raise HTTPException(status_code=500, detail="Failed to get skill requests")


@app.get("/skill-stats/{skill_id}")
async def get_skill_stats(skill_id: int):
    """Get statistics for a specific skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                # Get overall stats
                cur.execute(
                    """
                    SELECT
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout,
                        COUNT(CASE WHEN status = 'started' THEN 1 END) as in_progress,
                        SUM(questions_generated) as total_questions_generated,
                        AVG(processing_time_seconds) as avg_processing_time,
                        MAX(processing_time_seconds) as max_processing_time,
                        MIN(processing_time_seconds) as min_processing_time
                    FROM question_generation_requests
                    WHERE skill_id = %s
                """,
                    (skill_id,),
                )

                stats = cur.fetchone()

                # Get recent activity (last 24 hours)
                cur.execute(
                    """
                    SELECT COUNT(*) as recent_requests
                    FROM question_generation_requests
                    WHERE skill_id = %s AND created_at > %s
                """,
                    (skill_id, datetime.now() - timedelta(hours=24)),
                )

                recent_activity = cur.fetchone()

                return {
                    "skill_id": skill_id,
                    "total_requests": stats["total_requests"],
                    "completed": stats["completed"],
                    "failed": stats["failed"],
                    "timeout": stats["timeout"],
                    "in_progress": stats["in_progress"],
                    "success_rate": (
                        round((stats["completed"] / stats["total_requests"]) * 100, 2)
                        if stats["total_requests"] > 0
                        else 0
                    ),
                    "total_questions_generated": stats["total_questions_generated"]
                    or 0,
                    "avg_processing_time_seconds": round(
                        stats["avg_processing_time"] or 0, 2
                    ),
                    "max_processing_time_seconds": stats["max_processing_time"] or 0,
                    "min_processing_time_seconds": stats["min_processing_time"] or 0,
                    "recent_requests_24h": recent_activity["recent_requests"],
                }

    except Exception as e:
        logger.error(f"Error getting skill stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get skill statistics")


@app.get("/all-skills-stats")
async def get_all_skills_stats():
    """Get statistics for all skills"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                cur.execute(
                    """
                    SELECT
                        skill_id,
                        skill_name,
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout,
                        COUNT(CASE WHEN status = 'started' THEN 1 END) as in_progress,
                        SUM(questions_generated) as total_questions_generated,
                        AVG(processing_time_seconds) as avg_processing_time,
                        MAX(created_at) as last_request_at
                    FROM question_generation_requests
                    GROUP BY skill_id, skill_name
                    ORDER BY total_requests DESC
                """
                )

                skills_stats = cur.fetchall()

                return {
                    "skills": [
                        {
                            **dict(row),
                            "success_rate": (
                                round(
                                    (row["completed"] / row["total_requests"]) * 100, 2
                                )
                                if row["total_requests"] > 0
                                else 0
                            ),
                            "avg_processing_time_seconds": round(
                                row["avg_processing_time"] or 0, 2
                            ),
                        }
                        for row in skills_stats
                    ],
                    "total_skills": len(skills_stats),
                }

    except Exception as e:
        logger.error(f"Error getting all skills stats: {e}")
        raise HTTPException(status_code=500, detail="Failed to get skills statistics")


@app.get("/active-requests")
async def get_active_requests():
    """Get currently active (in-progress) requests"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                cur.execute(
                    """
                    SELECT * FROM question_generation_requests
                    WHERE status = 'started'
                    ORDER BY started_at ASC
                """
                )

                active_requests = cur.fetchall()

                return {
                    "active_requests": [dict(row) for row in active_requests],
                    "count": len(active_requests),
                }

    except Exception as e:
        logger.error(f"Error getting active requests: {e}")
        raise HTTPException(status_code=500, detail="Failed to get active requests")


@app.get("/dashboard")
async def get_dashboard():
    """Get dashboard overview of all request tracking data"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                # Overall stats
                cur.execute(
                    """
                    SELECT
                        COUNT(*) as total_requests,
                        COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                        COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed,
                        COUNT(CASE WHEN status = 'timeout' THEN 1 END) as timeout,
                        COUNT(CASE WHEN status = 'started' THEN 1 END) as in_progress,
                        SUM(questions_generated) as total_questions_generated,
                        AVG(processing_time_seconds) as avg_processing_time
                    FROM question_generation_requests
                """
                )

                overall_stats = cur.fetchone()

                # Recent activity (last 24 hours)
                cur.execute(
                    """
                    SELECT COUNT(*) as recent_requests
                    FROM question_generation_requests
                    WHERE created_at > %s
                """,
                    (datetime.now() - timedelta(hours=24),),
                )

                recent_activity = cur.fetchone()

                # Top skills by request count
                cur.execute(
                    """
                    SELECT skill_name, COUNT(*) as request_count
                    FROM question_generation_requests
                    GROUP BY skill_name
                    ORDER BY request_count DESC
                    LIMIT 5
                """
                )

                top_skills = cur.fetchall()

                # Failed tasks stats
                cur.execute(
                    "SELECT COUNT(*) as total_failed FROM failed_question_tasks"
                )
                failed_stats = cur.fetchone()

                return {
                    "overview": {
                        "total_requests": overall_stats["total_requests"],
                        "completed": overall_stats["completed"],
                        "failed": overall_stats["failed"],
                        "timeout": overall_stats["timeout"],
                        "in_progress": overall_stats["in_progress"],
                        "success_rate": (
                            round(
                                (
                                    overall_stats["completed"]
                                    / overall_stats["total_requests"]
                                )
                                * 100,
                                2,
                            )
                            if overall_stats["total_requests"] > 0
                            else 0
                        ),
                        "total_questions_generated": overall_stats[
                            "total_questions_generated"
                        ]
                        or 0,
                        "avg_processing_time_seconds": round(
                            overall_stats["avg_processing_time"] or 0, 2
                        ),
                        "recent_requests_24h": recent_activity["recent_requests"],
                        "total_failed_tasks": failed_stats["total_failed"],
                    },
                    "top_skills": [dict(row) for row in top_skills],
                    "timestamp": datetime.now().isoformat(),
                }

    except Exception as e:
        logger.error(f"Error getting dashboard data: {e}")
        raise HTTPException(status_code=500, detail="Failed to get dashboard data")


# ============================================================================
# HEALTH AND STATUS ENDPOINTS
# ============================================================================


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "service": "unified-question-generation-service",
        "components": ["worker", "dlq-monitor", "request-tracker"],
    }


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Unified Question Generation Service",
        "status": "running",
        "services": ["Question Generation Worker", "DLQ Monitor", "Request Tracker"],
    }


@app.get("/dapr/subscribe")
async def dapr_subscribe():
    """
    Dapr subscription endpoint.
    This tells Dapr what topics this service is subscribed to.
    """
    subscriptions = [
        {
            "pubsubname": "pubsub",
            "topic": "generate-questions",
            "route": "/generate-questions",
        },
        {
            "pubsubname": "pubsub",
            "topic": "generate-questions-dlq",
            "route": "/dlq-handler",
        },
    ]
    
    logger.info(f"Registering Dapr subscriptions: {subscriptions}")
    return subscriptions


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("WORKER_PORT", "8001"))
    uvicorn.run(app, host="0.0.0.0", port=port)
