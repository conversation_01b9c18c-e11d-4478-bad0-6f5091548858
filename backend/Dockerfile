FROM python:3.13-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONDONTWRITEBYTECODE=1

RUN groupadd -r appuser && useradd -r -g appuser appuser

# Install the dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    libpq-dev build-essential jq shc curl postgresql-client && \
    apt-get clean && rm -rf /var/lib/apt/lists/*

WORKDIR /app

ENV PYTHONPATH=/app

COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install -r requirements.txt

COPY . .

RUN chown -R appuser:appuser /app

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 8000

# Health check
HEALTHCHECK --interval=30s --timeout=5s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

CMD [ "sh", "-c", "set -e; echo '🚀 Waiting for database...'; while ! pg_isready -h \"$PG_HOST\" -p \"5432\" -U \"$POSTGRES_USER\" -d \"$POSTGRES_DB\" -q; do echo 'Database is unavailable - sleeping'; sleep 1; done; echo '✅ Database is ready.'; echo '🏃 Applying database migrations...'; python database/migration.py; echo '✅ Migrations applied.'; echo '🎉 Starting server...'; exec uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4" ]
