-- Update quiz_creation_logs table to rename columns and add foreign key

-- First, rename the columns
ALTER TABLE quiz_creation_logs RENAME COLUMN quiz_name TO assessment_name;
ALTER TABLE quiz_creation_logs RENAME COLUMN topic TO assessment_description;

-- Add foreign key constraint to assessments table
-- First, add assessment_id column
ALTER TABLE quiz_creation_logs ADD COLUMN assessment_id INTEGER;

-- Add foreign key constraint
ALTER TABLE quiz_creation_logs
ADD CONSTRAINT fk_quiz_creation_logs_assessment
FOREIGN KEY (assessment_id)
REFERENCES assessments(id)
ON DELETE CASCADE;

-- Add index for better performance
CREATE INDEX idx_quiz_creation_logs_assessment_id ON quiz_creation_logs(assessment_id);
