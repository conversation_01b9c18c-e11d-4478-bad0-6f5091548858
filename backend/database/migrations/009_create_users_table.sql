-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    external_id TEXT NOT NULL UNIQUE,
    email TEXT,
    display_name TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER update_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Seed data: Extract unique user_ids from user_assessment and quiz_creation_logs
INSERT INTO users (external_id)
SELECT DISTINCT user_id FROM user_assessment
UNION
SELECT DISTINCT user_id FROM quiz_creation_logs
ON CONFLICT (external_id) DO NOTHING;
