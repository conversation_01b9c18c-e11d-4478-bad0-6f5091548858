-- Add question selection mode to assessments table (if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'assessments' AND column_name = 'question_selection_mode'
    ) THEN
        ALTER TABLE assessments
        ADD COLUMN question_selection_mode VARCHAR(10) NOT NULL DEFAULT 'dynamic' CHECK (question_selection_mode IN ('fixed', 'dynamic'));
    END IF;
END $$;

-- Create assessment_questions junction table for fixed question sets
CREATE TABLE IF NOT EXISTS assessment_questions (
    id SERIAL PRIMARY KEY,
    assessment_id INTEGER NOT NULL REFERENCES assessments (id) ON DELETE CASCADE,
    question_id INTEGER NOT NULL REFERENCES questions (que_id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE (assessment_id, question_id)
);

-- Add index for faster lookups (if they don't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_assessment_questions_assessment_id'
    ) THEN
        CREATE INDEX idx_assessment_questions_assessment_id ON assessment_questions(assessment_id);
    END IF;

    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_assessment_questions_question_id'
    ) THEN
        CREATE INDEX idx_assessment_questions_question_id ON assessment_questions(question_id);
    END IF;
END $$;

-- Add comment to explain the question_selection_mode field (if it exists)
DO $$
BEGIN
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'assessments' AND column_name = 'question_selection_mode'
    ) THEN
        COMMENT ON COLUMN assessments.question_selection_mode IS
        'Determines how questions are selected for this assessment:
        - "fixed": Same pre-selected questions for every session
        - "dynamic": Questions randomly pulled from associated skills at session time';
    END IF;
END $$;
