-- Create user_assessment TABLE

CREATE TABLE IF NOT EXISTS user_assessment (
    id SERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    topic TEXT NOT NULL,
    level VARCHAR(50) NOT NULL,
    quiz_type VARCHAR(50) NOT NULL,
    que_id INTEGER NOT NULL REFERENCES questions(que_id) ON DELETE CASCADE,
    question TEXT NOT NULL,
    options JSONB NOT NULL,
    user_answer TEXT NOT NULL,
    correct_answer TEXT NOT NULL,
    result TEXT CHECK(result IN ('Correct', 'Incorrect', 'Timeout')),
    score INTEGER NOT NULL,
    time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
