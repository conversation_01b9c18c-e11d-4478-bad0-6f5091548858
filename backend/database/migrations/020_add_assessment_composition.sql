-- Add fields for assessment duration and question composition

ALTER TABLE assessments
ADD COLUMN duration_minutes INTEGER NOT NULL DEFAULT 60,
ADD COLUMN total_questions INTEGER NOT NULL DEFAULT 10,
ADD COLUMN composition JSONB DEFAULT '{"basic": 6, "intermediate": 6, "advanced": 8}'::JSONB,
ADD COLUMN passing_score INTEGER NOT NULL DEFAULT 70;

-- Add a trigger to validate that the composition values sum to total_questions
CREATE OR REPLACE FUNCTION validate_assessment_composition()
RETURNS TRIGGER AS $$
DECLARE
    total_from_composition INTEGER;
BEGIN
    -- Calculate the sum of all values in the composition JSONB
    SELECT COALESCE(SUM(value::INTEGER), 0)
    INTO total_from_composition
    FROM jsonb_each_text(NEW.composition);

    -- Check if the sum matches total_questions
    IF NEW.total_questions IS NULL THEN
        -- If total_questions is NULL, set it to the sum from composition
        NEW.total_questions := total_from_composition;
    ELSIF NEW.total_questions != total_from_composition THEN
        RAISE EXCEPTION 'The sum of question counts in composition (%) must equal total_questions (%)',
                        total_from_composition, NEW.total_questions;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER check_assessment_composition
    BEFORE INSERT OR UPDATE ON assessments
    FOR EACH ROW
    EXECUTE FUNCTION validate_assessment_composition();

-- Add a comment to explain the composition field
COMMENT ON COLUMN assessments.composition IS
'JSONB object containing the number of questions by difficulty level.
Example: {"basic": 6, "intermediate": 6, "advanced": 8}.
The sum of all values must equal total_questions.';
