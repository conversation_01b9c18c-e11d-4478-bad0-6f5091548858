-- Create sessions table
CREATE TABLE IF NOT EXISTS sessions (
    id SERIAL PRIMARY KEY,
    code CHAR(6) NOT NULL UNIQUE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    assessment_id INTEGER NOT NULL REFERENCES assessments(id) ON DELETE CASCADE,
    status TEXT NOT NULL CHECK (status IN ('pending', 'in_progress', 'completed', 'expired')),
    score INTEGER,
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TRIGGER update_sessions_updated_at
    BEFORE UPDATE ON sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create index for faster lookups by code
CREATE INDEX idx_sessions_code ON sessions(code);

-- Seed data: Create sessions from existing user_assessment data
WITH unique_assessments AS (
    SELECT DISTINCT
        user_id,
        topic,
        quiz_type,
        MIN(time) as first_attempt_time
    FROM user_assessment
    GROUP BY user_id, topic, quiz_type
)
INSERT INTO sessions (
    code,
    user_id,
    assessment_id,
    status,
    started_at,
    created_at
)
SELECT
    -- Generate a random 6-digit code
    LPAD(FLOOR(RANDOM() * 1000000)::TEXT, 6, '0'),
    u.id,
    a.id,
    'completed',
    ua.first_attempt_time,
    ua.first_attempt_time
FROM unique_assessments ua
JOIN users u ON ua.user_id = u.external_id
JOIN assessments a ON (
    (ua.quiz_type = 'final' AND a.name = ua.topic || ' Final Assessment') OR
    (ua.quiz_type != 'final' AND a.name = ua.topic || ' Mock Assessment')
)
ON CONFLICT DO NOTHING;
