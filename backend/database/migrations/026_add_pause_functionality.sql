-- Add pause functionality columns to sessions table
DO $$
BEGIN
    -- Add paused_at column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'paused_at'
    ) THEN
        ALTER TABLE sessions ADD COLUMN paused_at TIMESTAMP;
    END IF;

    -- Add total_paused_seconds column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'total_paused_seconds'
    ) THEN
        ALTER TABLE sessions ADD COLUMN total_paused_seconds INTEGER DEFAULT 0;
    END IF;

    -- Add pause_warning_sent column to track if warning was sent
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'sessions' AND column_name = 'pause_warning_sent'
    ) THEN
        ALTER TABLE sessions ADD COLUMN pause_warning_sent BOOLEAN DEFAULT FALSE;
    END IF;
END $$;

-- Add index for efficient cleanup queries
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_indexes
        WHERE indexname = 'idx_sessions_paused_at'
    ) THEN
        CREATE INDEX idx_sessions_paused_at ON sessions(paused_at) WHERE paused_at IS NOT NULL;
    END IF;
END $$;

-- Add comments to explain the pause functionality
COMMENT ON COLUMN sessions.paused_at IS 'Timestamp when the session was paused (NULL if not paused)';
COMMENT ON COLUMN sessions.total_paused_seconds IS 'Total accumulated pause time in seconds';
COMMENT ON COLUMN sessions.pause_warning_sent IS 'Whether a pause expiration warning has been sent to the user';
