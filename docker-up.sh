#!/bin/bash

# Load environment variables from backend/.env file
if [ -f "./backend/.env" ]; then
    # Export the specific variables that docker-compose needs
    SERVER_PORT=$(grep '^SERVER_PORT=' ./backend/.env | cut -d '=' -f2)
    export SERVER_PORT
    PG_DATABASE=$(grep '^PG_DATABASE=' ./backend/.env | cut -d '=' -f2)
    export PG_DATABASE
    PG_USER=$(grep '^PG_USER=' ./backend/.env | cut -d '=' -f2)
    export PG_USER
    PG_PASSWORD=$(grep '^PG_PASSWORD=' ./backend/.env | cut -d '=' -f2)
    export PG_PASSWORD

    echo "Loaded environment variables:"
    echo "SERVER_PORT=$SERVER_PORT"
    echo "PG_DATABASE=$PG_DATABASE"
    echo "PG_USER=$PG_USER"
    echo "PG_PASSWORD=***"

    # Run docker-compose with the exported variables
    docker-compose "$@"
else
    echo "Error: backend/.env file not found!"
    exit 1
fi
