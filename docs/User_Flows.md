# Herbit User Flows

This document outlines the primary user flows for both Administrators and Quiz Takers, mapping them to Command Line Interface (CLI) interactions and backend Application Programming Interface (API) endpoints.

## Admin User Flows (`herbit-admin` CLI)

The `herbit-admin` CLI interacts with the backend API to perform administrative tasks.

### 1. Skills Management

#### 1.1. Create a Skill
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Skills Management" -> "Create a Skill"
  2. Prompt: "Enter Skill Name"
  3. Prompt: "Enter Skill Description (required, will be used as topic for question generation if this skill is selected)"
- **API Endpoint:** `POST /create_skill`
  - **Request Body:** `{"name": "string", "description": "string"}`
  - **Response:** `{"skill": {"id": int, "name": "string", "description": "string"}, "message": "string"}`

#### 1.2. List Skills
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Skills Management" -> "List Skills"
  2. Output: Displays a list of available skills (ID, Name, Description).
- **API Endpoint:** `GET /get_skills`
  - **Response:** `{"skills": [{"id": int, "name": "string", "description": "string"}, ...]}`

#### 1.3. Map Skill to Assessment
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Skills Management" -> "Map Skill to Assessment"
  2. Prompt: Select an Assessment (from a list of existing assessments).
  3. Prompt: Select a Skill to Map (from a list of existing skills).
- **API Endpoint:** `POST /map_skill_to_assessment`
  - **Request Body:** `{"assessment_id": int, "skill_id": int}`
  - **Response:** `{"message": "string"}`

### 2. Assessments & Questions Management

This flow describes creating assessments and generating questions in a single step, as per the current `quiz_server.py` and `cli/admin/main.go`.

#### 2.1. Create Assessments (Mock & Final) and Generate Questions
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Assessments Management" -> "Create Assessments (Mock & Final)"
  2. Prompt: "Enter Assessment Name (e.g., DevOps Basics)" (this is the user-defined assessment name).
  3. Prompt: "Select a skill (its description will be used as the topic for question generation)" (this skill's description becomes the `topic` for question generation, and its ID is associated via `skill_ids`).
  4. Output: Displays success message, mock/final assessment IDs, the `assessment_base_name` (e.g., `UserDefinedName_DD_MM_YYYY`), and the name of the CSV file generated with all questions.
- **API Endpoint:** `POST /create_quiz`
  - **Request Body:** `{"quiz_name": "UserDefinedAssessmentName", "topic": "SkillDescriptionForQuestionContent", "user_id": "current_admin_username", "skill_ids": [selected_skill_id_for_topic_generation]}`
  - **Response:** `{"message": "string", "mock_assessment_id": int, "final_assessment_id": int, "file_data": "csv_content_string", "file_name": "all_question_AssessmentBaseName.csv", "assessment_base_name": "string"}`
  - *Note: This single step creates assessment shells (mock and final) and generates questions for them based on the provided skill description. The `assessment_base_name` is used as the `topic` in the `questions` table.*

#### 2.2. Add Final Questions to an Assessment
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Assessments Management" -> "Add Final Questions to an Assessment"
  2. Prompt: "Enter the question IDs (comma-separated, e.g. 1,2,3)" (referring to the CSV generated in step 2.1).
  3. Output: Displays success or error message.
- **API Endpoint:** `POST /insert_final_questions`
  - **Request Body:** `{"question_ids": [int, int, ...]}`
  - **Response:** `{"message": "string", "inserted_ids": [int, ...]} or {"error": "string"}`

### 3. Sessions Management

#### 3.1. Generate Session Codes for an Assessment
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Sessions Management" -> "Generate Session Codes for an Assessment"
  2. Prompt: Select an Assessment (from a list of existing assessments, e.g., "MyAssessment_DD_MM_YYYY Mock Assessment").
  3. Prompt: "Enter comma-separated list of usernames (no spaces)".
  4. Output: Displays success message and a list of usernames with their generated session codes.
- **API Endpoint:** `POST /generate_sessions`
  - **Request Body:** `{"assessment_id": int, "usernames": "user1,user2,..."}`
  - **Response:** `{"sessions": [{"username": "string", "session_code": "string", "session_db_id": int}, ...], "message": "string"}`

### 4. Reports Generation

#### 4.1. Date-wise Report
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Reports Generation" -> "Date-wise Report"
  2. Prompt: "Enter the date (DD-MM-YYYY)" (defaults to today).
  3. Prompt: Select report type ("Final Assessments" or "Mock Assessments").
  4. Output: Saves CSV report files (base and score) and prints their names.
- **API Endpoint:** `POST /generate_report`
  - **Request Body:** `{"report_type": "date_wise", "report_date": "DD-MM-YYYY", "quiz_type": "final|mock"}`
  - **Response:** `{"base_report": "csv_string", "score_report": "csv_string", "message": "optional_message_if_no_data"}`

#### 4.2. User-wise Report
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Reports Generation" -> "User-wise Report"
  2. Prompt: "Enter Username".
  3. Output: Saves CSV report files (base and score) and prints their names.
- **API Endpoint:** `POST /generate_report`
  - **Request Body:** `{"report_type": "user_wise", "user_name": "string"}`
  - **Response:** `{"base_report": "csv_string", "score_report": "csv_string", "message": "optional_message_if_no_data"}`

#### 4.3. Assessment-wise Report
- **CLI Interaction:**
  1. Navigate: `herbit-admin` -> "Reports Generation" -> "Assessment-wise Report"
  2. Prompt: "Enter the Base Assessment Name (e.g., MyAssessment_DD_MM_YYYY)". This refers to the `assessment_base_name` generated during assessment creation, which is used as the `topic` in the `questions` table and `user_assessment` table.
  3. Prompt: Select assessment type ("Final Assessment" or "Mock Assessment").
  4. Output: Saves CSV report files (base and score) and prints their names.
- **API Endpoint:** `POST /generate_report`
  - **Request Body:** `{"report_type": "quiz_wise", "report_topic": "AssessmentBaseName_DD_MM_YYYY", "quiz_type": "final|mock"}`
  - **Response:** `{"base_report": "csv_string", "score_report": "csv_string", "message": "optional_message_if_no_data"}`

## Quiz Taker User Flows (`quiz_user` CLI)

The `quiz_user` is typically a shell script (`quiz_user.sh`) compiled into a binary.

### 1. Start Quiz Attempt
- **CLI Interaction:**
  1. Execute: `./quiz_user`
  2. Prompt: "Please enter Quiz code:" (expects a 6-digit session code provided by the admin)
- **API Interaction (on entering session code):**
  - **Endpoint:** `POST /check_session_code`
    - **Request Body:** `{"session_code": "123456"}`
    - **Response:** `{"session_id": int, "assessment_id": int, "assessment_name": "string_full_assessment_name", "is_final": bool, "user_id_associated_with_session": int_internal_user_id, "session_status": "string"}`
    - The script uses `assessment_name` (e.g., "MyAssessment_DD_MM_YYYY Mock Assessment") and `is_final` to determine quiz context.

### 2. Answering Questions
- **CLI Interaction (loops for each question):**
  1. Displays: Question text, options (1-4), and a countdown timer.
  2. Prompt: "Enter your answer (1-4):" (with countdown timer)
  3. Displays: Feedback (Correct/Incorrect/Timeout) and the correct answer if applicable.
- **API Interactions:**
  - **To fetch questions:** `GET /get_questions/{session_code}?user_id={username}&difficulty={level}&retake={boolean}`
    - *Note: The `difficulty` parameter is used by the script to request questions of a specific level, often starting with 'easy' and potentially adapting. `retake` logic might influence question availability.*
    - **Response:** `{"quiz_type": "mock|final", "quiz_name": "string_full_assessment_name", "topic": "string_user_facing_topic_or_description", "question": [{"que_id": int, "question": "string", "options": {"a":"...", "b":"...", ...}, "level": "easy|intermediate|advanced"}, ...]}`
  - **To submit an answer:** `POST /check_and_save_answer`
    - **Request Body:** `{"user_id": "username_string", "question_id": "question_que_id_string", "answer": "chosen_option_char_or_timeout", "session_code": "session_code_string"}`
    - **Response:** `{"is_correct": bool, "correct_answer_key": "char", "correct_answer_value": "string", "question_id": int, "quiz_type": "mock|final"}`

### 3. View Quiz Results
- **CLI Interaction:**
  1. After all questions are answered or the quiz ends, the script displays a summary: total score, performance level, and potentially a brief review of answers.
  2. For mock tests, the script saves a detailed report as a JSON file.
- **API Interaction (typically called by the script at the end):**
  - **Endpoint:** `GET /get_progress?user_id={username}&assessment_name={full_assessment_name_from_check_session_code}`
    - **Response:** `{"total_score": int, "user_level": "string_performance_level", "progress_report": [{"question": "string", "options": {...}, "correct_answer": "char_key", "user_answer": "char_key_or_None", "result": "Correct|Incorrect|Timeout", "score": int}, ...]}`

## API Endpoints Summary

### Authentication
- `POST /check_user` - Verify if a user is in the list of allowed users (admin authentication)

### Skills Management
- `POST /create_skill` - Create a new skill
- `GET /get_skills` - Get all skills
- `POST /map_skill_to_assessment` - Map a skill to an assessment

### Assessments Management
- `POST /create_quiz` - Create new assessments (mock and final) and generate questions
- `GET /get_assessments` - Get all assessments
- `POST /insert_final_questions` - Add selected questions to a final assessment
- `GET /get_question_counts` - Get required question counts by difficulty level

### Sessions Management
- `POST /generate_sessions` - Generate session codes for users to take an assessment
- `POST /check_session_code` - Validate a session code and get assessment details

### Quiz Taking
- `GET /get_questions/{session_code}` - Get questions for a quiz session
- `POST /check_and_save_answer` - Submit and check an answer
- `GET /get_progress` - Get a user's progress and results for an assessment

### Reports
- `POST /generate_report` - Generate reports filtered by date, user, or assessment

### System
- `GET /get_value` - Get configuration values (question counts, time limits)
- `GET /health` - Health check endpoint for monitoring

## Data Flow and Relationships

The following diagram illustrates the key relationships between entities in the Herbit system:

```mermaid
graph TD
    A[Admin] -->|Creates| B[Skills]
    A -->|Creates| C[Assessments]
    B -->|Associated with| C
    C -->|Contains| D[Questions]
    A -->|Generates| E[Sessions]
    E -->|Links| F[Users]
    E -->|Links| C
    F -->|Takes Quiz via| E
    F -->|Answers| D
    A -->|Generates| G[Reports]
    G -->|Based on| F
    G -->|Based on| C
    G -->|Based on| D
```

## Adaptive Question Selection Logic

The quiz system adapts question difficulty based on user performance:

```mermaid
graph TD
    A[Start: Easy Question] -->|User Answers| B{Correct?}
    B -->|Yes| C[Next: Intermediate Question]
    B -->|No| D[Next: Easy Question]
    C -->|User Answers| E{Correct?}
    E -->|Yes| F[Next: Advanced Question]
    E -->|No| G[Next: Easy Question]
    F -->|User Answers| H{Correct?}
    H -->|Yes| I[Next: Advanced Question]
    H -->|No| J[Next: Intermediate Question]
```

This adaptive approach ensures that users are challenged appropriately based on their demonstrated knowledge level.
