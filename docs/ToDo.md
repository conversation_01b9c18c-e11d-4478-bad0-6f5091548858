# Herbit ToDo List

## Sprint Goals (Week of [Date])
Each sprint should focus on delivering value aligned with the roadmap and requirements. Sprints typically last one week.

- [ ] Add a question_selection_mode column to the assessments table (enum: 'fixed', 'dynamic'). Make appropriate changes in API and CLI.
- [ ] For fixed assessments, create an assessment_questions junction table to store pre-selected questions. Select set number of questions before finalizing assessment. Make appropriate changes in API and CLI.
- [ ] For the adaptive difficulty algorithm, add a difficulty_adaptation_enabled boolean column to assessments.
- [ ] Add a valid_until timestamp column to the sessions table to track expiration. Make appropriate changes in API and CLI to not allow assessments from expired sessions.
- [ ] Ensure only one active session per assessment session.
- [ ] Evaluate possibilty of replacing TEXT with more specific types where appropriate (VARCHAR with length limits).
- [ ] Add updated_at to tables that are missing it.
- [ ] Add created_by and updated_by columns to track which admin made changes.
- [ ] Create a dedicated audit_log table for tracking significant system events.

### Value Stream: Quiz Creation and Management
- [ ] Implement skill creation endpoint that stores skills in PostgreSQL database
- [ ] Develop skill listing functionality for administrators
- [ ] Create skill-to-assessment mapping feature
- [ ] Implement assessment creation workflow that generates mock and final test codes
- [ ] Develop LLM integration for question generation across all difficulty levels
- [ ] Implement question storage mechanism in PostgreSQL database
- [ ] Create final assessment question selection interface for admins
- [ ] Implement validation to ensure minimum question requirements per difficulty level

### Value Stream: Quiz Taking Experience
- [ ] Implement quiz code validation system for user access
- [ ] Develop single-question presentation interface with countdown timer
- [ ] Implement adaptive difficulty algorithm based on user performance
- [ ] Create answer tracking system with difficulty-based scoring
- [ ] Develop immediate feedback mechanism for answer correctness
- [ ] Implement quiz completion summary report generation

### Value Stream: Reporting and Analytics
- [ ] Develop date-filtered report generation functionality
- [ ] Implement user-specific report generation
- [ ] Create assessment-specific report filtering
- [ ] Develop detailed report system with performance metrics and question-level data
- [ ] Implement CSV export functionality for all report types
- [ ] Create quiz code listing feature for administrative reference

### Value Stream: Security Implementation
- [ ] Implement OIDC-based authentication system for administrators
- [ ] Develop secure environment variable management system
- [ ] Implement input validation across all API endpoints
- [ ] Ensure parameterized queries for all database interactions

### Value Stream: System Infrastructure
- [ ] Containerize application components for deployment
- [ ] Implement database migration system
- [ ] Develop robust API documentation
- [ ] Implement retry mechanism for LLM API calls
- [ ] Create comprehensive health check endpoint

### Future Enhancements
- [ ] Migrate remaining CLI tools to Go
- [ ] Implement ERPNext Training module integration
- [ ] Develop comprehensive user flow documentation
- [ ] Create detailed API documentation
- [ ] Implement enhanced analytics dashboard

## AI-Focused Tasks (# AI)
- [ ] Develop script to generate database schema documentation in mermaid format
- [ ] Create automated test case generator for API endpoints
- [ ] Implement intelligent code review assistant
- [ ] Develop automated documentation updater
- [ ] Create smart migration generator based on schema changes
