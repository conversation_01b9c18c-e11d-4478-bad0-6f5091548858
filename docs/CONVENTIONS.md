# git-commit-message-convention

- Make all commits in conventional commits style as the commit message title
- Include short summary of changes in the commit message

# Style Guide

- Write all python code complying to PEP8 standards
- Write all JavaScript code complying to prettier standards
- Use 2 spaces for indentation

# Database

- Write all the database schema changes into `migrations` as a new file. DO NOT change any existing migrations.
- Aim for at least 3NF normalization to minimize redundancy; denormalize selectively for performance-critical queries.
- Use UUIDs instead of SERIAL in distributed systems; leverage domain-specific types like TIMESTAMPTZ.
- Enforce constraints (`NOT NULL`, `UNIQUE`, foreign keys); validate data types rigorously (avoid `TEXT` for numeric fields).
- Implement security via schemas (`auth`, `analytics`); grant minimal permissions; hash/encrypt sensitive data like passwords.
- Model many-to-many with junction tables; use `ON DELETE CASCADE` only when necessary.
- Follow snake_case naming; avoid reserved keywords; prefix tables by domain (e.g., `finance_transactions`).
- Document via `COMMENT ON` and schema diagrams for complex systems in mermaid format inside `docs/Database.md` file.
- Include audit columns (`created_at`, `updated_at`); version control schema migrations with rollbacks.
- Optimize ORM queries to prevent N+1 issues; use stored procedures/triggers sparingly.
- Account for existing data when making changes to the schema.
- Use TIMESTAMPTZ instead of TIMESTAMP for all date/time fields to handle timezone issues.

# Repository Structure

<!-- - `api` contains the API server written in Go, and `web` contains a Nuxt.js webapp. -->
- Maintain `docs` directory in the root of the project that contains `README.md` as an index.
- Have `Requirements.md` file in `docs` that records all the requirements.
- Have `Roadmap.md` file in `docs` that contains the high-level roadmap of the application.
- Have `ToDo.md` file in `docs` that contains sprint goals, user stories and tasks for next 3 sprints based on the requirements and roadmap.
- Sprints typically last a week. Identify a clear goal focused on value delivery for every sprint and organize tasks and stories based on the goal.
- Identify features and small fixes that could be fixed by AI Agent, and append "# AI" to the stories/tasks that can be executed through AI Coding Agents.

# Other Notes

<!-- - Use Feature Flags using OpenFeature SDK. Development environment uses env variables to set features. -->
