# Herbit Development Guide

This document provides guidelines and instructions for developers working on the Herbit project.

## Development Setup

### Prerequisites

- Python 3.10+
- PostgreSQL 13+
- <PERSON>er and <PERSON><PERSON> Compose (optional)

### Local Development Setup

1. Clone the repository:
   ```bash
   git clone https://git.pride.improwised.dev/Improwised/herbit.git
   cd herbit
   ```

2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Copy the environment configuration:
   ```bash
   cp .env_example .env
   ```

5. Edit the `.env` file with your local configuration.

6. Run database migrations:
   ```bash
   python migration.py
   ```

7. Start the development server:
   ```bash
   uvicorn quiz_server:app --reload
   ```

## Project Structure

- `quiz_server.py`: Main FastAPI application
- `quiz_que_generate.py`: Question generation logic
- `questions_logging.py`: Database operations for questions
- `api_client.py`: Client for LLM API interactions
- `config.py`: Database and application configuration
- `migration.py`: Database migration script
- `migrations/`: SQL migration files
- `prompt.yaml`: LLM prompt templates

## Development Workflow

1. Create a new branch for your feature or bugfix:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes and test them locally.

3. Run linting and formatting:
   ```bash
   # Install pre-commit hooks
   pre-commit install

   # Run pre-commit on all files
   pre-commit run --all-files
   ```

4. Commit your changes following the conventional commits style:
   ```bash
   git commit -m "feat: add new feature"
   ```

5. Push your branch and create a pull request.

## Database Migrations

To create a new migration:

1. Create a new SQL file in the `migrations/` directory with a sequential number:
   ```
   migrations/008_your_migration_name.sql
   ```

2. Write your SQL statements in the file.

3. Run the migration:
   ```bash
   python migration.py
   ```

## Testing

Run tests with:

```bash
pytest
```

## Docker Development

Build and run the application with Docker Compose:

```bash
docker-compose up --build
```

## API Documentation

When the server is running, access the API documentation at:

- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Check your PostgreSQL service is running
   - Verify credentials in `.env` file
   - Ensure the database exists

2. **LLM API Issues**
   - Verify your API key is correct
   - Check the BASE_URL is accessible
   - Look for timeout or rate limiting errors

3. **Binary Conversion Failures**
   - Ensure `shc` is installed
   - Check file permissions
