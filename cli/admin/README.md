# HERBIT Admin CLI

This is a modern terminal user interface (TUI) for the HERBIT admin functionality, built with Go and the [Charm Bracelet Huh](https://github.com/charmbracelet/huh) framework.

## Features

- Interactive terminal UI with keyboard navigation
- All functionality from the original bash script
- Organized menu structure for better navigation
- Error handling and validation

## Installation

### Prerequisites

- Go 1.21 or higher

### Building from source

```bash
# Navigate to the admin CLI directory
cd cli/admin

# Build the admin CLI
go build -o herbit-admin

# Run the CLI
./herbit-admin
```

## Usage

The CLI provides the following main menu options:

1. **Assessments**
   - Create an Assessment
   - Add Final Questions

2. **Sessions**
   - Generate Sessions

3. **Skills**
   - Create a Skill
   - List Skills
   - Map Skill to Assessment

4. **Reports**
   - Date-wise Report
   - User-wise Report
   - Quiz-wise Report

5. **Quiz Codes**
   - Generate Quiz Code Report

## Environment Variables

- `SERVER_URL`: The URL of the HERBIT API server (default: "https://herbit.pride.improwised.dev")
