package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"os/user"
	"strconv"
	"strings"
	"time"

	"github.com/charmbracelet/huh"
	"github.com/charmbracelet/lipgloss"
)

// Table formatting constants
const (
	columnIDWidth      = 5
	columnNameWidth    = 25
	columnDescWidth    = 60
	columnTotalWidth   = columnIDWidth + columnNameWidth + columnDescWidth + 6 // 6 for spacing and borders
)

// Table styling functions
func renderTableLine(line string) string {
	return lipgloss.NewStyle().Width(columnTotalWidth).Render(line)
}

func renderTableRow(id, name, description string) string {
	idStyle := lipgloss.NewStyle().Width(columnIDWidth).Align(lipgloss.Right)
	nameStyle := lipgloss.NewStyle().Width(columnNameWidth)
	descStyle := lipgloss.NewStyle().Width(columnDescWidth).Align(lipgloss.Left)

	return fmt.Sprintf("%s %s %s",
		idStyle.Render(id),
		nameStyle.Render(name),
		descStyle.Render(description),
	)
}

// ServerURL is the API endpoint for the quiz server
var ServerURL = getEnvOrDefault("SERVER_URL", "https://herbit.pride.improwised.dev")

// Models for API responses
type Skill struct {
	ID          int    `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

type SkillsResponse struct {
	Skills []Skill `json:"skills"`
}

type Assessment struct {
	ID              int             `json:"id"`
	Name            string          `json:"name"`
	Description     string          `json:"description"`
	IsFinal         bool            `json:"is_final"`
	TotalQuestions  int             `json:"total_questions"`
	DurationMinutes int             `json:"duration_minutes"`
	PassingScore    int             `json:"passing_score"`
	Composition     json.RawMessage `json:"composition"`
}

type AssessmentsResponse struct {
	Assessments []Assessment `json:"assessments"`
}

type CreateQuizResponse struct {
	Message            string `json:"message"` // Updated message
	MockAssessmentID   int    `json:"mock_assessment_id"`
	FinalAssessmentID  int    `json:"final_assessment_id"`
	FileData           string `json:"file_data"`
	FileName           string `json:"file_name"`
	AssessmentBaseName string `json:"assessment_base_name"`
}

type SessionInfo struct { // Renamed from Session for clarity
	Username    string `json:"username"`
	SessionCode string `json:"session_code"` // Field name changed from 'code'
	SessionDbID int    `json:"session_db_id"`
}

type GenerateSessionsResponse struct {
	Sessions []SessionInfo `json:"sessions"`
	Message  string        `json:"message"`
}

type QuestionCountsResponse struct {
	Easy         int `json:"easy"`
	Intermediate int `json:"intermediate"`
	Advanced     int `json:"advanced"`
}

// Helper functions
func getEnvOrDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

func getCurrentUsername() string {
	currentUser, err := user.Current()
	if err != nil {
		return "unknown"
	}

	return currentUser.Username
}

func fetchSkills() ([]Skill, error) {
	resp, err := http.Get(ServerURL + "/get_skills")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var skillsResp SkillsResponse
	if err := json.Unmarshal(body, &skillsResp); err != nil {
		// Try to unmarshal into an error response if primary unmarshal fails
		var errResp struct {
			Error string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Error != "" {
			return nil, fmt.Errorf("API error: %s", errResp.Error)
		}
		return nil, fmt.Errorf("failed to parse skills response: %v. Body: %s", err, string(body))
	}

	return skillsResp.Skills, nil
}

func fetchAssessments() ([]Assessment, error) {
	resp, err := http.Get(ServerURL + "/get_assessments")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var assessmentsResp AssessmentsResponse
	if err := json.Unmarshal(body, &assessmentsResp); err != nil {
		var errResp struct {
			Error string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Error != "" {
			return nil, fmt.Errorf("API error: %s", errResp.Error)
		}
		return nil, fmt.Errorf("failed to parse assessments response: %v. Body: %s", err, string(body))
	}

	return assessmentsResp.Assessments, nil
}

func fetchQuestionCounts() (*QuestionCountsResponse, error) {
	resp, err := http.Get(ServerURL + "/get_question_counts")
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var countsResp QuestionCountsResponse
	if err := json.Unmarshal(body, &countsResp); err != nil {
		var errResp struct {
			Error string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Error != "" {
			return nil, fmt.Errorf("API error: %s", errResp.Error)
		}
		return nil, fmt.Errorf("failed to parse question counts response: %v. Body: %s", err, string(body))
	}

	return &countsResp, nil
}

func createSkill(name, description string) error {
	payload := map[string]string{
		"name":        name,
		"description": description,
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", ServerURL+"/create_skill", strings.NewReader(string(jsonPayload)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Detail string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Detail != "" {
			return fmt.Errorf("API error: %s", errResp.Detail)
		}
		return fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}
	fmt.Println(string(body)) // Print success message from API
	return nil
}

func createQuiz(quizName, skillDescriptionTopic string, skillID int) (*CreateQuizResponse, error) {
	payload := map[string]interface{}{
		"quiz_name": quizName, // User-defined assessment name
		"user_id":   getCurrentUsername(),
		"topic":     skillDescriptionTopic, // Skill description for question generation
		"skill_ids": []int{skillID},
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", ServerURL+"/create_quiz", strings.NewReader(string(jsonPayload)))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{
		Timeout: 900 * time.Second, // 15 minutes timeout
	}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Detail interface{} `json:"detail"`
		} // detail can be string or object
		if json.Unmarshal(body, &errResp) == nil && errResp.Detail != nil {
			return nil, fmt.Errorf("API error: %v", errResp.Detail)
		}
		return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var createResp CreateQuizResponse
	if err := json.Unmarshal(body, &createResp); err != nil {
		return nil, fmt.Errorf("failed to parse create quiz response: %v. Body: %s", err, string(body))
	}

	// Save the file data
	if createResp.FileData != "" && createResp.FileName != "" {
		err = ioutil.WriteFile(createResp.FileName, []byte(createResp.FileData), 0644)
		if err != nil {
			// Return response anyway, but include file save error in message
			createResp.Message = fmt.Sprintf("%s (Warning: failed to save questions file: %v)", createResp.Message, err)
			return &createResp, nil
		}
	}

	return &createResp, nil
}


func generateSessions(assessmentID int, usernames string) (*GenerateSessionsResponse, error) {
	payload := map[string]interface{}{
		"assessment_id": assessmentID,
		"usernames":     usernames,
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}

	req, err := http.NewRequest("POST", ServerURL+"/generate_sessions", strings.NewReader(string(jsonPayload)))
	if err != nil {
		return nil, err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Detail string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Detail != "" {
			return nil, fmt.Errorf("API error: %s", errResp.Detail)
		}
		return nil, fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var sessionsResp GenerateSessionsResponse
	if err := json.Unmarshal(body, &sessionsResp); err != nil {
		return nil, fmt.Errorf("failed to parse generate sessions response: %v. Body: %s", err, string(body))
	}

	return &sessionsResp, nil
}

func insertFinalQuestions(questionIDs []int) error {
	payload := map[string]interface{}{
		"question_ids": questionIDs,
	}
	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", ServerURL+"/insert_final_questions", strings.NewReader(string(jsonPayload)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, _ := ioutil.ReadAll(resp.Body)
	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Detail string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Detail != "" {
			return fmt.Errorf("API error: %s", errResp.Detail)
		}
		return fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}
	fmt.Println(string(body)) // Print success message from API
	return nil
}

func generateReport(reportType, reportDate, userName, reportTopicBaseName, quizType string) error {
	payload := map[string]string{
		"report_type": reportType,
	}

	if reportDate != "" {
		payload["report_date"] = reportDate
	}
	if userName != "" {
		payload["user_name"] = userName
	}
	if reportTopicBaseName != "" {
		payload["report_topic"] = reportTopicBaseName // API expects base name for quiz_wise
	}
	if quizType != "" { // 'mock' or 'final'
		payload["quiz_type"] = quizType
	}

	jsonPayload, err := json.Marshal(payload)
	if err != nil {
		return err
	}

	req, err := http.NewRequest("POST", ServerURL+"/generate_report", strings.NewReader(string(jsonPayload)))
	if err != nil {
		return err
	}
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		var errResp struct {
			Detail string `json:"detail"`
		}
		if json.Unmarshal(body, &errResp) == nil && errResp.Detail != "" {
			return fmt.Errorf("API error: %s", errResp.Detail)
		}
		return fmt.Errorf("API error (status %d): %s", resp.StatusCode, string(body))
	}

	var response struct {
		BaseReport  string `json:"base_report"`
		ScoreReport string `json:"score_report"`
		Message     string `json:"message,omitempty"`
	}
	if err := json.Unmarshal(body, &response); err != nil {
		return fmt.Errorf("failed to parse generate report response: %v. Body: %s", err, string(body))
	}

	if response.Message != "" && response.BaseReport == "" && response.ScoreReport == "" {
		fmt.Println(response.Message) // E.g., "No assessment data found..."
		return nil
	}

	// Generate filenames based on report type
	var baseReportFile, scoreReportFile string
	filenamePrefix := ""
	switch reportType {
	case "date_wise":
		filenamePrefix = fmt.Sprintf("%s_%s", quizType, reportDate)
	case "user_wise":
		filenamePrefix = fmt.Sprintf("user_%s", userName)
	case "quiz_wise":
		filenamePrefix = fmt.Sprintf("%s_%s", quizType, reportTopicBaseName)
	default:
		filenamePrefix = "report"
	}
	baseReportFile = fmt.Sprintf("%s_base_report.csv", filenamePrefix)
	scoreReportFile = fmt.Sprintf("%s_score_report.csv", filenamePrefix)

	// Save to files
	if response.BaseReport != "" {
		err = ioutil.WriteFile(baseReportFile, []byte(response.BaseReport), 0644)
		if err != nil {
			return fmt.Errorf("failed to save base report: %v", err)
		}
		fmt.Printf("Base Report saved to %s\n", baseReportFile)
	} else {
		fmt.Println("No base report data to save.")
	}

	if response.ScoreReport != "" {
		err = ioutil.WriteFile(scoreReportFile, []byte(response.ScoreReport), 0644)
		if err != nil {
			return fmt.Errorf("failed to save score report: %v", err)
		}
		fmt.Printf("Score Report saved to %s\n", scoreReportFile)
	} else {
		fmt.Println("No score report data to save.")
	}

	if response.BaseReport != "" || response.ScoreReport != "" {
		fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("10")).Render("✓ Report generated successfully!"))
	}

	return nil
}

// UI Components
func displayLogo() {
	logo := `
                 ___________
                |           \
                |            \
 _   _   _____  |   __  __    \  ____    _   _______
| | | | |  ___| |  |__||__|   / |  _ \  | | |__   __|
| |_| | | |_    |     __     /  | |_) | | |    | |
|  _  | |  _|   |           /   |  _ <  | |    | |
| | | | | |___  |     |\    \   | |_) | | |    | |
|_| |_| |_____| |     | \    \  |____/  |_|    |_|
                |     |  \    \
                |     |   \    \
                |_____|    \____\


 Highly Engineered Robot Built for Internal Tests
`
	fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("#96CDFB")).Render(logo))
}

func assessmentsMenu() error {
	var choice string
	err := huh.NewSelect[string]().
		Title("Assessments Management").
		Options(
			huh.NewOption("Create Assessments (Mock & Final)", "create"),
			huh.NewOption("Add Final Questions to an Assessment", "final"),
			huh.NewOption("Back to Main Menu", "back"),
		).
		Value(&choice).
		Run()

	if err != nil {
		return err
	}

	switch choice {
	case "create":
		return createAssessmentForm()
	case "final":
		return addFinalQuestionsForm()
	case "back":
		return nil
	}

	return nil
}

func createAssessmentForm() error {
	var userDefinedAssessmentName string // e.g. "DevOps Basics"
	var selectedSkillID int

	skills, err := fetchSkills()
	if err != nil {
		return fmt.Errorf("failed to fetch skills: %v", err)
	}

	if len(skills) == 0 {
		fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("9")).Render("No skills available. Please create a skill first via the 'Skills' menu."))
		return nil // Not an error, just can't proceed
	}

	skillOptions := make([]huh.Option[int], len(skills))
	for i, skill := range skills {
		skillOptions[i] = huh.NewOption(fmt.Sprintf("%d: %s - %s", skill.ID, skill.Name, skill.Description), skill.ID)
	}

	err = huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("Enter Assessment Name (e.g., DevOps Basics)").
				Placeholder("My Assessment").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("assessment name cannot be empty")
					}
					return nil
				}).
				Value(&userDefinedAssessmentName),

			huh.NewSelect[int]().
				Title("Select a skill (its description will be used as the topic for question generation)").
				Options(skillOptions...).
				Value(&selectedSkillID),
		),
	).Run()

	if err != nil {
		return err // User cancelled or other form error
	}

	var selectedSkillDescription string
	for _, skill := range skills {
		if skill.ID == selectedSkillID {
			selectedSkillDescription = skill.Description
			break
		}
	}

	if selectedSkillDescription == "" {
		return fmt.Errorf("selected skill has no description, which is required for question generation")
	}

	fmt.Println("Creating assessments and generating questions... This may take several minutes.")

	response, err := createQuiz(userDefinedAssessmentName, selectedSkillDescription, selectedSkillID)
	if err != nil {
		return fmt.Errorf("failed to create assessment: %v", err)
	}

	fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("10")).Render("✓ " + response.Message))
	fmt.Printf("Assessment Base Name: %s\n", response.AssessmentBaseName)
	fmt.Printf("Mock Assessment ID: %d\n", response.MockAssessmentID)
	fmt.Printf("Final Assessment ID: %d\n", response.FinalAssessmentID)
	if response.FileName != "" {
		fmt.Printf("All generated questions saved to: %s\n", response.FileName)
	}
	fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("12")).Render("➡️ Next Step: Use the 'Sessions' menu to generate session codes for these assessments."))
	return nil
}

func addFinalQuestionsForm() error {
	var questionIDsInput string

	counts, err := fetchQuestionCounts()
	if err != nil {
		return fmt.Errorf("failed to fetch question counts: %v", err)
	}

	err = huh.NewForm(
		huh.NewGroup(
			huh.NewNote().
				Title("Final Question Requirements").
				Description(fmt.Sprintf("For a standard final assessment, ensure your selection includes at least %d easy, %d intermediate, and %d advanced questions.",
					counts.Easy, counts.Intermediate, counts.Advanced)),

			huh.NewInput().
				Title("Enter the question IDs (comma-separated, e.g. 1,2,3)").
				Placeholder("1,5,10,23,30...").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("question IDs cannot be empty")
					}
					// Basic validation for comma-separated numbers
					parts := strings.Split(s, ",")
					for _, part := range parts {
						if _, err := strconv.Atoi(strings.TrimSpace(part)); err != nil {
							return fmt.Errorf("invalid format: '%s' is not a number. Ensure IDs are comma-separated numbers", part)
						}
					}
					return nil
				}).
				Value(&questionIDsInput),
		),
	).Run()

	if err != nil {
		return err
	}

	idStrings := strings.Split(questionIDsInput, ",")
	questionIDs := make([]int, 0, len(idStrings))
	for _, idStr := range idStrings {
		id, err := strconv.Atoi(strings.TrimSpace(idStr))
		if err != nil {
			// Should be caught by validator, but double check
			return fmt.Errorf("invalid question ID '%s': %v", idStr, err)
		}
		questionIDs = append(questionIDs, id)
	}

	// The API will do the detailed count validation.
	// minRequired := counts.Easy + counts.Intermediate + counts.Advanced
	// if len(questionIDs) < minRequired {
	// 	return fmt.Errorf("not enough questions. Need at least %d questions. You provided %d", minRequired, len(questionIDs))
	// }

	err = insertFinalQuestions(questionIDs)
	if err != nil {
		return fmt.Errorf("failed to add final questions: %v", err)
	}

	// Success message is printed by insertFinalQuestions on OK response
	return nil
}

func sessionsMenu() error {
	var choice string
	err := huh.NewSelect[string]().
		Title("Sessions Management").
		Options(
			huh.NewOption("Generate Session Codes for an Assessment", "generate"),
			huh.NewOption("Back to Main Menu", "back"),
		).
		Value(&choice).
		Run()

	if err != nil {
		return err
	}

	switch choice {
	case "generate":
		return generateSessionsForm()
	case "back":
		return nil
	}
	return nil
}

func generateSessionsForm() error {
	var selectedAssessmentID int
	var usernamesInput string

	assessments, err := fetchAssessments()
	if err != nil {
		return fmt.Errorf("failed to fetch assessments: %v", err)
	}

	if len(assessments) == 0 {
		fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("9")).Render("No assessments available. Please create an assessment first via the 'Assessments' menu."))
		return nil
	}

	assessmentOptions := make([]huh.Option[int], len(assessments))
	for i, asmnt := range assessments {
		assessmentOptions[i] = huh.NewOption(fmt.Sprintf("%d: %s (%s)", asmnt.ID, asmnt.Name, map[bool]string{true: "Final", false: "Mock"}[asmnt.IsFinal]), asmnt.ID)
	}

	err = huh.NewForm(
		huh.NewGroup(
			huh.NewSelect[int]().
				Title("Select an Assessment to Generate Sessions For").
				Options(assessmentOptions...).
				Value(&selectedAssessmentID),

			huh.NewInput().
				Title("Enter comma-separated list of usernames (no spaces)").
				Placeholder("user1,user2,user3").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("usernames cannot be empty")
					}
					return nil
				}).
				Value(&usernamesInput),
		),
	).Run()

	if err != nil {
		return err
	}

	response, err := generateSessions(selectedAssessmentID, usernamesInput)
	if err != nil {
		return fmt.Errorf("failed to generate sessions: %v", err)
	}

	fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("10")).Render("✓ " + response.Message))
	fmt.Println("Generated Session Codes:")
	for _, session := range response.Sessions {
		fmt.Printf("  User: %s | Session Code: %s\n", session.Username, session.SessionCode)
	}
	return nil
}

func skillsMenu() error {
	var choice string
	err := huh.NewSelect[string]().
		Title("Skills Management").
		Options(
			huh.NewOption("Create a Skill", "create"),
			huh.NewOption("List Skills", "list"),
			huh.NewOption("Back to Main Menu", "back"),
		).
		Value(&choice).
		Run()

	if err != nil {
		return err
	}

	switch choice {
	case "create":
		return createSkillForm()
	case "list":
		return listSkills()
	case "back":
		return nil
	}
	return nil
}

func createSkillForm() error {
	var name, description string

	err := huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("Enter Skill Name").
				Placeholder("My Skill").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("skill name cannot be empty")
					}
					return nil
				}).
				Value(&name),

			huh.NewInput().
				Title("Enter Skill Description (required, will be used as topic for question generation)").
				Placeholder("A brief description of the skill").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("skill description is required")
					}
					return nil
				}).
				Value(&description),
		),
	).Run()

	if err != nil {
		return err
	}

	err = createSkill(name, description)
	if err != nil {
		return fmt.Errorf("failed to create skill: %v", err)
	}
	// Success message printed by createSkill
	return nil
}

func listSkills() error {
	skills, err := fetchSkills()
	if err != nil {
		return fmt.Errorf("failed to fetch skills: %v", err)
	}

	if len(skills) == 0 {
		fmt.Println("No skills found.")
		return nil
	}

	// Table header
	headerStyle := lipgloss.NewStyle().Bold(true).Underline(true)
	fmt.Println(renderTableLine("┌" + strings.Repeat("─", columnIDWidth-1) + "┬" + strings.Repeat("─", columnNameWidth) + "┬" + strings.Repeat("─", columnDescWidth-1) + "┐"))
	fmt.Println(renderTableRow("ID", "Name", "Description"))
	fmt.Println(renderTableLine("├" + strings.Repeat("─", columnIDWidth-1) + "┼" + strings.Repeat("─", columnNameWidth) + "┼" + strings.Repeat("─", columnDescWidth-1) + "┤"))

	// Table rows
	for i, skill := range skills {
		// Apply alternating background color for better readability
		if i%2 == 0 {
			rowStyle := lipgloss.NewStyle().Background(lipgloss.Color("236"))
			fmt.Println(rowStyle.Render(renderTableRow(
				strconv.Itoa(skill.ID),
				headerStyle.Render(skill.Name),
				skill.Description,
			)))
		} else {
			fmt.Println(renderTableRow(
				strconv.Itoa(skill.ID),
				headerStyle.Render(skill.Name),
				skill.Description,
			))
		}
	}

	// Table footer
	fmt.Println(renderTableLine("└" + strings.Repeat("─", columnIDWidth-1) + "┴" + strings.Repeat("─", columnNameWidth) + "┴" + strings.Repeat("─", columnDescWidth-1) + "┘"))
	fmt.Println(lipgloss.NewStyle().Faint(true).Render(fmt.Sprintf("Total skills: %d", len(skills))))

	return nil
}


func reportsMenu() error {
	var choice string
	err := huh.NewSelect[string]().
		Title("Reports").
		Options(
			huh.NewOption("Date-wise Report", "date"),
			huh.NewOption("User-wise Report", "user"),
			huh.NewOption("Assessment-wise Report", "quiz"), // Renamed for clarity
			huh.NewOption("Back to Main Menu", "back"),
		).
		Value(&choice).
		Run()

	if err != nil {
		return err
	}

	switch choice {
	case "date":
		return dateWiseReportForm()
	case "user":
		return userWiseReportForm()
	case "quiz":
		return assessmentWiseReportForm() // Renamed function
	case "back":
		return nil
	}
	return nil
}

func dateWiseReportForm() error {
	var reportDate string
	var quizType string // 'mock' or 'final'

	today := time.Now().Format("02-01-2006")

	err := huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("Enter the date (DD-MM-YYYY)").
				Placeholder(today).
				Value(&reportDate).
				Validate(func(s string) error {
					if s == "" {
						return nil
					} // Allow empty for default
					_, err := time.Parse("02-01-2006", s)
					if err != nil {
						return fmt.Errorf("invalid date format, please use DD-MM-YYYY")
					}
					return nil
				}),

			huh.NewSelect[string]().
				Title("Select the report type (for assessments on that date)").
				Options(
					huh.NewOption("Final Assessments", "final"),
					huh.NewOption("Mock Assessments", "mock"),
				).
				Value(&quizType),
		),
	).Run()

	if err != nil {
		return err
	}

	if reportDate == "" {
		reportDate = today
	}

	err = generateReport("date_wise", reportDate, "", "", quizType)
	if err != nil {
		return fmt.Errorf("failed to generate date-wise report: %v", err)
	}
	return nil
}

func userWiseReportForm() error {
	var userName string

	err := huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("Enter Username").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("username cannot be empty")
					}
					return nil
				}).
				Value(&userName),
		),
	).Run()

	if err != nil {
		return err
	}

	// For user_wise, quizType is not sent to API as it fetches all for user.
	// Filenames will be based on username.
	err = generateReport("user_wise", "", userName, "", "")
	if err != nil {
		return fmt.Errorf("failed to generate user-wise report: %v", err)
	}
	return nil
}

func assessmentWiseReportForm() error { // Renamed from quizWiseReportForm
	var assessmentBaseName string // e.g., "DevOps Basics_10_08_2024"
	var quizType string           // 'mock' or 'final'

	err := huh.NewForm(
		huh.NewGroup(
			huh.NewInput().
				Title("Enter the Base Assessment Name (e.g., MyAssessment_DD_MM_YYYY)").
				Placeholder("MyAssessment_10_08_2024").
				Validate(func(s string) error {
					if s == "" {
						return fmt.Errorf("assessment base name cannot be empty")
					}
					return nil
				}).
				Value(&assessmentBaseName),

			huh.NewSelect[string]().
				Title("Select the assessment type").
				Options(
					huh.NewOption("Final Assessment", "final"),
					huh.NewOption("Mock Assessment", "mock"),
				).
				Value(&quizType),
		),
	).Run()

	if err != nil {
		return err
	}

	err = generateReport("quiz_wise", "", "", assessmentBaseName, quizType)
	if err != nil {
		return fmt.Errorf("failed to generate assessment-wise report: %v", err)
	}
	return nil
}

func mainMenu() error {
	var choice string
	// "Quiz Codes" option removed
	err := huh.NewSelect[string]().
		Title("Herbit Admin Control Panel - Choose an action").
		Options(
			huh.NewOption("Assessments Management", "assessments"),
			huh.NewOption("Sessions Management (Generate Codes)", "sessions"),
			huh.NewOption("Skills Management", "skills"),
			huh.NewOption("Reports Generation", "reports"),
			huh.NewOption("Exit", "exit"),
		).
		Value(&choice).
		Run()

	if err != nil {
		if err == huh.ErrUserAborted { // Handle Ctrl+C
			fmt.Println("Exiting...")
			os.Exit(0)
		}
		return err
	}

	var menuError error
	switch choice {
	case "assessments":
		menuError = assessmentsMenu()
	case "sessions":
		menuError = sessionsMenu()
	case "skills":
		menuError = skillsMenu()
	case "reports":
		menuError = reportsMenu()
	case "exit":
		fmt.Println("Exiting Herbit Admin CLI. Goodbye!")
		os.Exit(0)
	}
	return menuError // Return error from submenu to main loop for display
}

func main() {
	// Check if user is authenticated (basic check, API does real auth)
	currentUserName := getCurrentUsername()

	displayLogo()

	fmt.Printf("Welcome, %s!\n\n", currentUserName)

	// Main application loop
	for {
		err := mainMenu()
		if err != nil {
			// Check if it's a user abort error from a sub-form
			if err == huh.ErrUserAborted {
				fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("12")).Render("\nOperation cancelled. Returning to main menu."))
			} else {
				fmt.Println(lipgloss.NewStyle().Foreground(lipgloss.Color("9")).Render("\nError: " + err.Error()))
			}
		}
		// Always print a newline for spacing before showing main menu again, unless exiting
		if err != huh.ErrUserAborted && err != nil { // Add some space if there was an error message
			fmt.Println()
		} else if err == nil { // Add space if a submenu completed successfully
			fmt.Println()
		}
		// If err is huh.ErrUserAborted from mainMenu itself, it's handled in mainMenu.
	}
}
