# Herbit

Herbit stands for "Highly Engineered Robot Built for Internal Tests". It is a combination of API server and CLI application that is responsible for creating assessments for specified skills. It uses LLMs to create multiple-choice-questions and stores them in the database. It asks the skill facilitator to select a skill and generate an assessment with 30 questions.

## Setup Instructions

### 1. <PERSON>lone the Repository

Clone the git repository using the following command:

```bash
git clone https://git.pride.improwised.dev/Improwised/herbit.git
```

---

### 2. Navigate to the Project Directory

Move into the cloned directory with:

```bash
cd herbit
```

---

### 3. Set Up Environment Configuration

**Step 1: Configure Backend Environment**

Navigate to the backend directory and set up the environment configuration:

```bash
cd backend
cp .env_example .env
```

**Step 2: Edit the `backend/.env` file with your specific settings**

Replace <your_api_key> with your API key from llmui

```bash
BASE_URL=https://llmui.pride.improwised.dev
API_KEY=<your_api_key>
```

**_Get Your API Key_**

Retrieve and configure the API key:

1. Visit [llmui.pride.improwised.dev](https://llmui.pride.improwised.dev/)
2. Go to **Settings** > **Account** > **API Keys**
3. If no API key exists:
   - Click the **Create API Key** button to generate a new one
   - Copy the generated key
4. If you already have an API key, simply copy it
5. Update the `API_KEY` value in `backend/.env` file

---

**Step 3: Configure Frontend Environment**

```bash
cd ../frontend
cp .env_example .env
```

Edit the `frontend/.env` file if needed (default values would usually work):

**Step 4: Return to Project Root**

```bash
cd ..
```

---

### 4. Install Docker (If Not Already Installed)

**For Ubuntu/Debian:**

```bash
sudo apt-get update
sudo apt-get install docker.io docker-compose-plugin
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

**For Arch Linux:**

```bash
sudo pacman -S docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

**For CentOS/RHEL:**

```bash
sudo yum install docker docker-compose
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER
```

> **Note**: After adding yourself to the docker group, log out and log back in for the changes to take effect.

**Verify Docker Installation:**

```bash
docker --version
docker-compose --version
```

---

### 5. Start the Application with Docker

**Step 1: Make the Docker Script Executable**

```bash
chmod +x docker-up.sh
```

**Step 2: Start All Services**

Use the provided Docker script to start all services:

```bash
./docker-up.sh up -d
```

**Expected Output**: You should see Docker pulling images and starting containers:

```
Creating network "herbit_herbit-network" with driver "bridge"
Creating volume "herbit-db-data" with default driver
Creating herbit-db ... done
Creating herbit-redis ... done
Creating herbit-ldap ... done
Creating herbit-dex ... done
Creating herbit-backend ... done
Creating herbit-worker ... done
Creating herbit-frontend ... done
Creating herbit-adminer ... done
```

**Step 3: Verify All Services Are Running**

Check that all containers are running properly:

```bash
docker-compose ps
```

**Expected Output**: All services should show "Up" status.

---

### 6. Access the Application

**🌐 Web Interface**

- **Frontend**: http://localhost:5173
- **API Documentation**: http://localhost:8000/docs
- **Question Generation**: http://localhost:8001/docs
- **Database Admin**: http://localhost:8080 (Adminer)

**🔍 Health Checks**

Verify each service is healthy:

```bash
# Check backend health
curl http://localhost:8000/health

# Check all container status
docker-compose ps
```

---

### 7. User Quiz Access

**Web Access (Recommended):**

Users can access quizzes through the web interface at:

```
http://your-server-ip:5173
```

**Instructions for Quiz Takers:**

1. Open your web browser
2. Navigate to: `http://your-server-ip:5173`
3. Enter your quiz code when prompted
4. Complete the assessment

**Mobile Access:**

- The web interface is mobile-responsive
- Works on tablets and smartphones
- No app installation required

---

## Docker Management Commands

### Essential Docker Commands

**Start all services:**

```bash
./docker-up.sh up -d
```

**Stop all services:**

```bash
./docker-up.sh down
```

**View logs:**

```bash
# All services
./docker-up.sh logs

# Specific service
./docker-up.sh logs backend
./docker-up.sh logs frontend
```

**Restart a service:**

```bash
./docker-up.sh restart backend
```

**Update and rebuild:**

```bash
./docker-up.sh down
./docker-up.sh up -d --build
```

**Clean up (removes all data):**

```bash
./docker-up.sh down -v
docker system prune -a
```

---

## Testing & Verification

### Run Test Suite

**Inside Docker Container:**

```bash
docker-compose exec backend python -m pytest -v tests/apiTest.py
```

**Direct Testing:**

```bash
# Test API endpoints
curl http://localhost:8000/health
curl http://localhost:8000/docs

# Test frontend
curl http://localhost:5173
```

**Expected Output**: All tests should pass with green checkmarks.

---

## Advanced Configuration

### Environment Customization

Edit `backend/.env` to modify application behavior:

**Question Distribution:**

```env
EASY_QUESTIONS_COUNT=10        # Number of easy questions
INTERMEDIATE_QUESTIONS_COUNT=10 # Number of intermediate questions
ADVANCED_QUESTIONS_COUNT=10     # Number of advanced questions
TOTAL_QUESTIONS_COUNT=30        # Total questions (should equal sum above)
```

**AI Model Selection:**

```env
MODEL_ID='qwen/qwen3-30b-a3b'  # LLM model for question generation
```

### Performance Tuning

**For High Load (Production):**

```bash
# Scale backend workers
./docker-up.sh up -d --scale backend=3 --scale worker=2
```

**For Development:**

```bash
# Enable hot reload
./docker-up.sh up -d --build
```

### Custom Ports

Edit `docker-compose.yaml` to change default ports:

```yaml
ports:
  - "8080:8000" # Backend on port 8080
  - "3000:5173" # Frontend on port 3000
```

---

## Troubleshooting

### Common Issues

**❌ "Port already in use" errors**

```bash
# Check what's using the port
sudo lsof -i :8000
sudo lsof -i :5173

# Stop conflicting services or change ports in docker-compose.yaml
```

**❌ Database connection failed**

```bash
# Check database container
docker-compose logs db

# Restart database
./docker-up.sh restart db
```

**❌ Services won't start**

```bash
# Check all container logs
./docker-up.sh logs

# Check system resources
docker system df
docker system prune
```

**❌ Frontend not loading**

```bash
# Check frontend logs
./docker-up.sh logs frontend

# Rebuild frontend
./docker-up.sh up -d --build frontend
```

**❌ CLI Admin tool not working**

```bash
# Ensure Go is installed
go version

# Rebuild the CLI tool
cd cli/admin
go build -o herbit-admin
```

### Getting Help

1. **Check container logs**: `./docker-up.sh logs [service-name]`
2. **Verify environment variables**: `docker-compose config`
3. **Check container health**: `docker-compose ps`
4. **Monitor resources**: `docker stats`

### Debugging Commands

```bash
# Enter a running container
docker-compose exec backend bash
docker-compose exec frontend sh

# Check environment variables inside container
docker-compose exec backend env

# View container resource usage
docker stats
```

---

## Quick Start Checklist

- [ ] Repository cloned
- [ ] `backend/.env` file configured with API key and database settings
- [ ] `frontend/.env` file configured (optional, defaults usually work)
- [ ] Docker and Docker Compose installed
- [ ] Docker script made executable (`chmod +x docker-up.sh`)
- [ ] All services started (`./docker-up.sh up -d`)
- [ ] All containers running (`docker-compose ps`)
- [ ] Web interface accessible (http://localhost:5173)
- [ ] API documentation accessible (http://localhost:8000/docs)
- [ ] Database admin accessible (http://localhost:8080)
- [ ] CLI admin tool built (optional: `cd cli/admin && go build -o herbit-admin`)
- [ ] Test suite passes (`docker-compose exec backend python -m pytest -v tests/apiTest.py`)

**🎉 You're ready to create and manage assessments with Docker!**

---

## Production Deployment

### Security Considerations

1. **Change default passwords** in `backend/.env`
2. **Use HTTPS** in production
3. **Configure firewall** rules
4. **Regular backups** of database volume
5. **Monitor logs** for security issues

### Backup and Restore

**Backup database:**

```bash
docker-compose exec db pg_dump -U herbit_user herbit_db > backup.sql
```

**Restore database:**

```bash
docker-compose exec -T db psql -U herbit_user herbit_db < backup.sql
```

### Monitoring

**View real-time logs:**

```bash
./docker-up.sh logs -f
```

**Monitor resource usage:**

```bash
docker stats
```

---

## Architecture Overview

### Services

- **Backend**: FastAPI application (Port 8000)
- **Frontend**: Vue.js application (Port 5173)
- **Database**: PostgreSQL (Port 5433 external, 5432 internal)
- **Redis**: Cache and session storage (Port 6380 external, 6379 internal)
- **Worker**: Background task processor (Port 8001)
- **Dex**: Authentication service (Port 5556)
- **LDAP**: User directory (Port 1389)
- **Adminer**: Database admin interface (Port 8080)

### Data Flow

1. Users access the frontend at port 5173
2. Frontend communicates with backend API at port 8000
3. Backend stores data in PostgreSQL database
4. Authentication handled by Dex service
5. Background tasks processed by worker service
6. CLI admin tool communicates directly with backend API
